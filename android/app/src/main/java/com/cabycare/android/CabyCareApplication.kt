package com.cabycare.android

import android.app.Application
import android.util.Log
import com.cabycare.android.di.AppInitializer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * CabyCare应用程序入口点
 * 负责应用程序的全局初始化和配置
 */
@HiltAndroidApp
class CabyCareApplication : Application() {

    @Inject
    lateinit var appInitializer: AppInitializer

    companion object {
        private const val TAG = "CabyCareApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        Log.i(TAG, "📱 CabyCare应用启动")
        
        // 初始化应用程序组件
        initializeComponents()
    }
    
    /**
     * 初始化应用程序组件
     */
    private fun initializeComponents() {
        // 初始化应用程序核心组件
        appInitializer.initialize()

        // 这里可以添加其他全局初始化逻辑
        // 例如：崩溃报告、分析工具、日志系统等
        Log.d(TAG, "✅ 应用程序组件初始化完成")
    }
}
