package com.cabycare.android.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Pets
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.ByteArrayInputStream

/**
 * 猫咪头像组件
 * 类似iOS版本的CatAvatarView，支持带认证的图片加载
 */
@Composable
fun CatAvatarView(
    avatarUrl: String?,
    size: Dp = 60.dp,
    cornerRadius: Dp = 12.dp,
    modifier: Modifier = Modifier,
    refreshTrigger: Int = 0,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient
) {
    var imageBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var hasError by remember { mutableStateOf(false) }
    var lastLoadedUrl by remember { mutableStateOf("") }
    
    // 监听URL变化和刷新触发器
    LaunchedEffect(avatarUrl, refreshTrigger) {
        // URL变化时清除缓存并重新加载
        if (avatarUrl != lastLoadedUrl) {
            imageBitmap = null
            lastLoadedUrl = ""
            hasError = false
        }
        
        loadImageIfNeeded(
            avatarUrl = avatarUrl,
            userPreferences = userPreferences,
            okHttpClient = okHttpClient,
            onImageLoaded = { bitmap ->
                imageBitmap = bitmap
                lastLoadedUrl = avatarUrl ?: ""
                hasError = false
            },
            onLoadingChanged = { loading ->
                isLoading = loading
            },
            onError = {
                hasError = true
                isLoading = false
            }
        )
    }
    
    Box(
        modifier = modifier
            .size(size)
            .clip(RoundedCornerShape(cornerRadius)),
        contentAlignment = Alignment.Center
    ) {
        if (imageBitmap != null) {
            // 显示加载的图片
            Image(
                bitmap = imageBitmap!!.asImageBitmap(),
                contentDescription = "猫咪头像",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        } else {
            // 占位符
            AvatarPlaceholder(
                isLoading = isLoading,
                hasError = hasError,
                size = size,
                cornerRadius = cornerRadius
            )
        }
    }
}

/**
 * 头像占位符
 */
@Composable
private fun AvatarPlaceholder(
    isLoading: Boolean,
    hasError: Boolean,
    size: Dp,
    cornerRadius: Dp
) {
    Box(
        modifier = Modifier
            .size(size)
            .clip(RoundedCornerShape(cornerRadius))
            .background(MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)),
        contentAlignment = Alignment.Center
    ) {
        when {
            isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(size * 0.4f),
                    color = MaterialTheme.colorScheme.primary,
                    strokeWidth = 2.dp
                )
            }
            hasError -> {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "加载失败",
                    modifier = Modifier.size(size * 0.4f),
                    tint = MaterialTheme.colorScheme.error
                )
            }
            else -> {
                Icon(
                    imageVector = Icons.Default.Pets,
                    contentDescription = "猫咪头像",
                    modifier = Modifier.size(size * 0.4f),
                    tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
                )
            }
        }
    }
}

/**
 * 加载带认证的图片
 */
private suspend fun loadImageIfNeeded(
    avatarUrl: String?,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient,
    onImageLoaded: (Bitmap) -> Unit,
    onLoadingChanged: (Boolean) -> Unit,
    onError: () -> Unit
) {
    if (avatarUrl.isNullOrEmpty()) return
    
    onLoadingChanged(true)
    
    try {
        // 构建请求（不需要手动添加认证头，OkHttpClient的AuthInterceptor会自动处理）
        val request = Request.Builder()
            .url(avatarUrl)
            .get()
            .build()

        Log.d("CatAvatarView", "发起图片请求: ${avatarUrl.take(50)}... (认证头将由AuthInterceptor自动添加)")
        
        // 在IO线程中执行网络请求
        withContext(Dispatchers.IO) {
            val response = okHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                response.body?.bytes()?.let { bytes ->
                    val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                    if (bitmap != null) {
                        withContext(Dispatchers.Main) {
                            onImageLoaded(bitmap)
                            onLoadingChanged(false)
                        }
                        Log.d("CatAvatarView", "✅ 猫咪头像加载成功: ${avatarUrl.take(50)}...")
                    } else {
                        withContext(Dispatchers.Main) {
                            onError()
                        }
                        Log.e("CatAvatarView", "❌ 无法解码图片数据")
                    }
                } ?: run {
                    withContext(Dispatchers.Main) {
                        onError()
                    }
                    Log.e("CatAvatarView", "❌ 响应体为空")
                }
            } else {
                withContext(Dispatchers.Main) {
                    onError()
                }
                Log.e("CatAvatarView", "❌ 图片请求失败: ${response.code}")
            }
            response.close()
        }
    } catch (e: Exception) {
        withContext(Dispatchers.Main) {
            onError()
        }
        Log.e("CatAvatarView", "❌ 加载猫咪头像失败: ${e.message}", e)
    }
}

/**
 * 圆形猫咪头像（快捷版本）
 */
@Composable
fun CircularCatAvatarView(
    avatarUrl: String?,
    size: Dp = 60.dp,
    modifier: Modifier = Modifier,
    refreshTrigger: Int = 0,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient
) {
    CatAvatarView(
        avatarUrl = avatarUrl,
        size = size,
        cornerRadius = size / 2, // 设置为圆形
        modifier = modifier,
        refreshTrigger = refreshTrigger,
        userPreferences = userPreferences,
        okHttpClient = okHttpClient
    )
} 