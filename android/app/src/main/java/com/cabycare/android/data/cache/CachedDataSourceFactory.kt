package com.cabycare.android.data.cache

import android.content.Context
import android.util.Log
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import com.cabycare.android.data.cache.VideoCache
import com.cabycare.android.data.local.UserPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 支持缓存的数据源工厂
 * 为HLS流提供自动缓存功能
 */
@Singleton
class CachedDataSourceFactory @Inject constructor(
    @ApplicationContext private val context: Context,
    private val videoCache: VideoCache,
    private val userPreferences: UserPreferences
) : DataSource.Factory {

    companion object {
        private const val TAG = "CachedDataSourceFactory"
    }

    /**
     * 获取当前有效的访问令牌
     */
    private fun getCurrentAccessToken(): String? {
        return runCatching {
            runBlocking {
                // 首先尝试从完整用户信息获取最新token
                val tokenFromCompleteInfo = userPreferences.getAccessTokenFromCompleteInfo().first()
                if (!tokenFromCompleteInfo.isNullOrEmpty()) {
                    Log.d(TAG, "✅ 使用完整用户信息中的token: ${tokenFromCompleteInfo.take(20)}...")
                    return@runBlocking tokenFromCompleteInfo
                }

                // 向后兼容：从旧存储方式获取
                val tokenFromOldStorage = userPreferences.getAccessToken().first()
                if (!tokenFromOldStorage.isNullOrEmpty()) {
                    Log.d(TAG, "✅ 使用旧存储方式的token: ${tokenFromOldStorage.take(20)}...")
                    return@runBlocking tokenFromOldStorage
                }

                Log.w(TAG, "⚠️ 未找到有效的访问令牌")
                return@runBlocking null
            }
        }.getOrNull()
    }
    
    /**
     * 创建数据源
     */
    override fun createDataSource(): DataSource {
        // 创建HTTP数据源工厂
        val httpDataSourceFactory = DefaultHttpDataSource.Factory().apply {
            // 设置用户代理
            setUserAgent("CabyCare-Android/1.0")
            
            // 添加认证头（实时获取最新token）
            val currentToken = getCurrentAccessToken()
            if (currentToken != null) {
                setDefaultRequestProperties(mapOf(
                    "Authorization" to "Bearer $currentToken"
                ))
                Log.d(TAG, "🔑 HTTP数据源已配置认证头")
            } else {
                Log.w(TAG, "⚠️ HTTP数据源未配置认证头，token为空")
            }
            
            // 优化网络连接设置
            setConnectTimeoutMs(10000) // 10秒连接超时（更快失败检测）
            setReadTimeoutMs(20000)    // 20秒读取超时（适中的读取时间）
            setAllowCrossProtocolRedirects(true)
            setKeepPostFor302Redirects(true)

            // 添加更多网络优化设置
            setDefaultRequestProperties(mapOf(
                "Connection" to "keep-alive",
                "Cache-Control" to "no-cache"
            ))
        }
        
        // 创建缓存数据源
        return CacheDataSource.Factory()
            .setCache(videoCache.cache)
            .setUpstreamDataSourceFactory(httpDataSourceFactory)
            .setFlags(CacheDataSource.FLAG_BLOCK_ON_CACHE or CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            .createDataSource().also {
                Log.d(TAG, "✅ 创建带缓存的数据源")
            }
    }
    
    /**
     * 创建仅用于网络的数据源（不使用缓存）
     */
    fun createNetworkOnlyDataSource(): DataSource {
        return DefaultHttpDataSource.Factory().apply {
            setUserAgent("CabyCare-Android/1.0")

            // 添加认证头（实时获取最新token）
            val currentToken = getCurrentAccessToken()
            if (currentToken != null) {
                setDefaultRequestProperties(mapOf(
                    "Authorization" to "Bearer $currentToken"
                ))
                Log.d(TAG, "🔑 网络数据源已配置认证头")
            } else {
                Log.w(TAG, "⚠️ 网络数据源未配置认证头，token为空")
            }
            
            setConnectTimeoutMs(15000)
            setReadTimeoutMs(30000)
            setAllowCrossProtocolRedirects(true)
            setKeepPostFor302Redirects(true)
        }.createDataSource().also {
            Log.d(TAG, "🌐 创建仅网络数据源（无缓存）")
        }
    }
}
