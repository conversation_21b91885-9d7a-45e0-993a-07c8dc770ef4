package com.cabycare.android.ui.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.AccessibleDevice
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.data.model.CatStatistics
import com.cabycare.android.data.model.CatAlert
import com.cabycare.android.data.model.ComparisonResult
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.CatRepository
import com.cabycare.android.data.repository.DeviceRepository
import com.cabycare.android.data.repository.UserRepository
import com.cabycare.android.data.service.CatStatisticsService
import com.cabycare.android.data.local.UserPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import javax.inject.Inject

/**
 * 警报严重程度
 */
enum class AlertSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * 猫咪统计数据（现在使用真实的如厕数据）
 */
data class CatStatistic(
    val catId: String,
    val name: String,
    val age: String,
    val gender: String,
    val healthStatus: String,
    val todayActivity: Int, // 今日如厕次数
    val yesterdayActivity: Int, // 昨日如厕次数
    val healthScore: Int, // 基于如厕频率和规律性的健康评分
    val activityLevel: String, // 活跃度等级
    val avatarUrl: String? = null // 猫咪头像URL
) {
    /**
     * 如厕次数对比结果
     */
    val toiletComparison: ComparisonResult
        get() = when {
            todayActivity > yesterdayActivity -> ComparisonResult.INCREASED
            todayActivity < yesterdayActivity -> ComparisonResult.DECREASED
            else -> ComparisonResult.SAME
        }
}

/**
 * 首页UI状态
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val alertCats: List<com.cabycare.android.data.model.CatAlert> = emptyList(),
    val catStats: List<CatStatistic> = emptyList(),
    val catStatistics: List<CatStatistics> = emptyList(), // 新的统计数据
    val isCalculating: Boolean = false
) {
    /**
     * 是否有数据
     */
    val hasData: Boolean
        get() = catStatistics.isNotEmpty() || alertCats.isNotEmpty()

    /**
     * 是否显示空状态
     */
    val showEmptyState: Boolean
        get() = !isLoading && !hasData && error == null
}

/**
 * 首页ViewModel
 * 现在使用真实的猫咪如厕数据而不是假数据
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val catRepository: CatRepository,
    private val deviceRepository: DeviceRepository,
    private val userRepository: UserRepository,
    private val catStatisticsService: CatStatisticsService, // 注入统计计算服务
    val userPreferences: UserPreferences, // 提供给UI使用
    val okHttpClient: OkHttpClient // 提供给UI使用
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    companion object {
        private const val TAG = "HomeViewModel"
    }
    
    /**
     * 加载数据 - 使用真实的如厕统计数据
     */
    fun loadData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // 获取当前用户ID
                val userId = userRepository.getUserId().first()
                if (userId.isNullOrEmpty()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "用户未登录"
                    )
                    return@launch
                }

                // 获取猫咪列表
                when (val catsResult = catRepository.getCats()) {
                    is NetworkResult.Success -> {
                        val cats = catsResult.data
                        
                        if (cats.isEmpty()) {
                            // 如果没有猫咪，显示空状态
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                catStats = emptyList(),
                                alertCats = emptyList(),
                                error = null
                            )
                            return@launch
                        }
                        
                        // 开始计算真实的统计数据
                        _uiState.value = _uiState.value.copy(isCalculating = true)
                        
                        // 使用CatStatisticsService计算真实的统计数据
                        val (catStats, alerts) = catStatisticsService.calculateAllCatStatistics(cats, userId)

                        // 同时获取新的统计数据格式
                        val statisticsResult = catRepository.getCatStatistics()
                        val alertsResult = catRepository.getCatAlerts()

                        val newStatistics = when (statisticsResult) {
                            is NetworkResult.Success -> statisticsResult.data
                            else -> emptyList()
                        }

                        val newAlerts = when (alertsResult) {
                            is NetworkResult.Success -> alertsResult.data
                            else -> emptyList()
                        }

                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isCalculating = false,
                            catStats = catStats,
                            alertCats = alerts,
                            catStatistics = newStatistics,
                            error = null
                        )
                    }
                    is NetworkResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isCalculating = false,
                            error = catsResult.exception.message ?: "加载猫咪数据失败"
                        )
                    }
                    else -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isCalculating = false,
                            error = "未知错误"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isCalculating = false,
                    error = e.message ?: "加载失败"
                )
            }
        }
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadData()
    }
}
