package com.cabycare.android.di

import android.content.Context
import com.cabycare.android.data.cache.CachedDataSourceFactory
import com.cabycare.android.data.cache.VideoCache
import com.cabycare.android.data.local.UserPreferences
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 缓存模块
 * 提供视频缓存相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object CacheModule {
    
    @Provides
    @Singleton
    fun provideVideoCache(
        @ApplicationContext context: Context
    ): VideoCache {
        return VideoCache(context)
    }
    
    @Provides
    @Singleton
    fun provideCachedDataSourceFactory(
        @ApplicationContext context: Context,
        videoCache: VideoCache,
        userPreferences: UserPreferences
    ): CachedDataSourceFactory {
        return CachedDataSourceFactory(context, videoCache, userPreferences)
    }
}
