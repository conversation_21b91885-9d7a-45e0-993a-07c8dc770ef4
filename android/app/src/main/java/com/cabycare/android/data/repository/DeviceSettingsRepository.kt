package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.api.DeviceSettingsApiService
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.NetworkResult
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设备设置数据仓库
 */
@Singleton
class DeviceSettingsRepository @Inject constructor(
    private val apiService: DeviceSettingsApiService,
    private val userPreferences: UserPreferences
) {
    companion object {
        private const val TAG = "DeviceSettingsRepository"
    }

    /**
     * 获取设备状态
     */
    suspend fun getDeviceStatus(deviceId: String): NetworkResult<DeviceStatusResponse> {
        return try {
            Log.d(TAG, "获取设备状态: $deviceId")
            val response = apiService.getDeviceStatus(deviceId)
            Log.d(TAG, "设备状态获取成功: ${response.name}")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "获取设备状态失败", e)
            NetworkResult.Error(e)
        }
    }

    /**
     * 更新设备基本信息
     */
    suspend fun updateDeviceBasicInfo(
        deviceId: String,
        name: String? = null
    ): NetworkResult<DeviceStatusResponse> {
        return try {
            val userId = userPreferences.getUserId().first()
            if (userId.isNullOrEmpty()) {
                return NetworkResult.Error(Exception("用户未登录"))
            }

            Log.d(TAG, "更新设备基本信息: $deviceId, name=$name")
            val request = DeviceBasicInfoUpdateRequest(name = name)
            val response = apiService.updateDeviceBasicInfo(deviceId, userId, request)
            Log.d(TAG, "设备基本信息更新成功")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "更新设备基本信息失败", e)
            NetworkResult.Error(e)
        }
    }

    /**
     * 获取设备OTA设置
     */
    suspend fun getDeviceOTASettings(deviceId: String): NetworkResult<DeviceOTASettings> {
        return try {
            val userId = userPreferences.getUserId().first()
            if (userId.isNullOrEmpty()) {
                return NetworkResult.Error(Exception("用户未登录"))
            }

            Log.d(TAG, "获取设备OTA设置: $deviceId")

            val response = apiService.getDeviceOTASettings(deviceId, userId)
            Log.d(TAG, "OTA设置获取成功")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "获取设备OTA设置失败", e)
            NetworkResult.Error(e)
        }
    }

    /**
     * 更新设备OTA设置
     */
    suspend fun updateDeviceOTASettings(
        deviceId: String,
        autoOtaEnabled: Boolean,
        idleUpdateStartHour: Int? = null,
        idleUpdateEndHour: Int? = null
    ): NetworkResult<DeviceOTASettings> {
        return try {
            val userId = userPreferences.getUserId().first()
            if (userId.isNullOrEmpty()) {
                return NetworkResult.Error(Exception("用户未登录"))
            }

            Log.d(TAG, "更新设备OTA设置: $deviceId, enabled=$autoOtaEnabled")
            val request = SimpleOTAUpdateRequest(
                enabled = autoOtaEnabled,
                idleUpdateStartHour = idleUpdateStartHour,
                idleUpdateEndHour = idleUpdateEndHour
            )
            val response = apiService.updateDeviceOTASettings(deviceId, userId, request)
            Log.d(TAG, "OTA设置更新成功")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "更新设备OTA设置失败", e)
            NetworkResult.Error(e)
        }
    }

    /**
     * 获取设备OTA状态
     */
    suspend fun getDeviceOTAStatus(deviceId: String): NetworkResult<DeviceOTAStatusResponse> {
        return try {
            Log.d(TAG, "获取设备OTA状态: $deviceId")
            val response = apiService.getDeviceOTAStatus(deviceId)
            Log.d(TAG, "OTA状态获取成功: ${response.otaStatus.displayName}")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "获取设备OTA状态失败", e)
            NetworkResult.Error(e)
        }
    }

    /**
     * 获取设备传感器状态
     */
    suspend fun getDeviceSensorStatus(deviceId: String): NetworkResult<DeviceSensorStatusResponse> {
        return try {
            val userId = userPreferences.getUserId().first()
            if (userId.isNullOrEmpty()) {
                return NetworkResult.Error(Exception("用户未登录"))
            }

            Log.d(TAG, "获取设备传感器状态: $deviceId")
            val response = apiService.getDeviceSensorStatus(deviceId, userId)
            Log.d(TAG, "传感器状态获取成功: 错误数量=${response.errorCount}")
            NetworkResult.Success(response)
        } catch (e: Exception) {
            Log.e(TAG, "获取设备传感器状态失败", e)
            NetworkResult.Error(e)
        }
    }
}
