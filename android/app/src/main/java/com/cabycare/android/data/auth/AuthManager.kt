package com.cabycare.android.data.auth

import android.content.Context
import android.util.Log
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.model.OAuthCallbackResponse
import com.cabycare.android.data.model.User
import com.cabycare.android.data.model.UserInfoResponse
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.network.safeApiCall
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.Base64
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Token更新监听器接口
 */
interface TokenUpdateListener {
    fun onTokenUpdated(newToken: String)
    fun onTokenCleared()
}

/**
 * 认证状态数据类
 */
data class AuthState(
    val isAuthenticated: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val user: User? = null,
    val accessToken: String? = null
)

/**
 * 认证管理器
 * 负责用户认证、令牌管理和自动刷新
 */
@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences,
    private val apiService: ApiService,
    private val okHttpClient: OkHttpClient,
    private val logtoManager: LogtoManager
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    companion object {
        private const val TAG = "AuthManager"
        private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }
    
    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val refreshMutex = Mutex()
    private var refreshTask: kotlinx.coroutines.Deferred<String>? = null

    // Token更新监听器列表
    private val tokenUpdateListeners = mutableListOf<TokenUpdateListener>()
    
    init {
        // 调试信息
        com.cabycare.android.debug.LogtoDebugHelper.printConfig()
        com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()

        // 初始化时检查认证状态
        checkInitialAuthStatus()
    }
    
    /**
     * 检查初始认证状态
     */
    private fun checkInitialAuthStatus() {
        scope.launch {
            val isAuthenticated = userPreferences.isAuthenticated().first()

            if (isAuthenticated) {
                Log.i(TAG, "🔍 用户已认证，获取最新令牌")
                // 直接获取最新的令牌，不依赖本地存储的过期检查
                val currentToken = getCurrentAccessToken()
                if (currentToken != null) {
                    Log.i(TAG, "🔑 获取到有效令牌，用户已登录")
                    updateAuthState(isAuthenticated = true)
                    loadUserProfile()
                } else {
                    Log.e(TAG, "❌ 无法获取有效令牌，需要重新登录")
                    clearAuthCredentials()
                }
            } else {
                Log.i(TAG, "🔒 用户未登录")
                updateAuthState(isAuthenticated = false)
            }
        }
    }
    
    /**
     * 检查令牌是否有效
     */
    private fun isTokenValid(token: String): Boolean {
        return try {
            val parts = token.split(".")
            if (parts.size != 3) return false
            
            val payload = String(Base64.getUrlDecoder().decode(parts[1]))
            val json = Json.parseToJsonElement(payload).jsonObject
            val exp = json["exp"]?.jsonPrimitive?.content?.toLongOrNull() ?: return false
            
            val expirationTime = exp * 1000 // 转换为毫秒
            val currentTime = System.currentTimeMillis()
            
            expirationTime > currentTime + TOKEN_REFRESH_THRESHOLD
        } catch (e: Exception) {
            Log.e(TAG, "令牌验证失败", e)
            false
        }
    }
    
    /**
     * 开始OAuth2.0登录流程
     */
    suspend fun startLogin(): String {
        Log.i(TAG, "🎯 开始OAuth2.0登录流程")
        updateAuthState(isLoading = true)
        
        return try {
            val authUrl = buildAuthorizationUrl()
            Log.i(TAG, "🔗 生成授权URL: $authUrl")
            authUrl
        } catch (e: Exception) {
            Log.e(TAG, "❌ 生成授权URL失败", e)
            updateAuthState(isLoading = false, error = e.message)
            throw e
        }
    }
    
    /**
     * 构建授权URL
     */
    private fun buildAuthorizationUrl(): String {
        val state = java.util.UUID.randomUUID().toString()
        val codeVerifier = generateCodeVerifier()
        val codeChallenge = generateCodeChallenge(codeVerifier)
        
        // 保存状态和code verifier
        kotlinx.coroutines.runBlocking {
            userPreferences.saveAuthState(state)
            userPreferences.saveCodeVerifier(codeVerifier)
        }
        
        return "${LogtoConfig.LOGTO_ENDPOINT}/oidc/auth?" +
                "client_id=${LogtoConfig.CLIENT_ID}&" +
                "redirect_uri=${LogtoConfig.REDIRECT_URI}&" +
                "response_type=code&" +
                "scope=openid profile email offline_access&" +
                "state=$state&" +
                "code_challenge=$codeChallenge&" +
                "code_challenge_method=S256&" +
                "prompt=consent"
    }
    
    /**
     * 处理授权回调
     */
    suspend fun handleAuthCallback(code: String, state: String): NetworkResult<Unit> {
        Log.i(TAG, "🔄 处理授权回调")
        updateAuthState(isLoading = true)
        
        return try {
            // 验证state
            val savedState = userPreferences.getAuthState().first()
            if (state != savedState) {
                throw SecurityException("State参数不匹配")
            }
            
            // 交换授权码获取令牌
            val tokens = exchangeCodeForTokens(code)

            // 立即保存令牌到所有存储位置
            userPreferences.saveAccessToken(tokens.accessToken)
            tokens.refreshToken?.let { userPreferences.saveRefreshToken(it) }

            // 立即更新完整用户信息中的token
            userPreferences.updateTokenInfo(
                accessToken = tokens.accessToken,
                refreshToken = tokens.refreshToken,
                expiresIn = tokens.expiresIn ?: 3600
            )

            // 立即更新认证状态，确保所有API调用使用新token
            updateAuthState(
                isAuthenticated = true,
                accessToken = tokens.accessToken
            )

            // 通知NetworkManager token已更新
            notifyTokenUpdated(tokens.accessToken)

            // 调用后端callback API获取user_id
            Log.i(TAG, "🔄 调用后端callback API获取user_id...")
            val callbackResult = handleOAuthCallback(code, state)
            when (callbackResult) {
                is NetworkResult.Success -> {
                    val callbackResponse = callbackResult.data
                    Log.i(TAG, "✅ 后端callback成功，获得user_id: ${callbackResponse.userId}")

                    // 保存后端返回的user_id
                    userPreferences.saveUserId(callbackResponse.userId)

                    // 获取用户信息（这里应该使用后端的user_id）
                    val userResult = getUserProfile()
                    when (userResult) {
                        is NetworkResult.Success -> {
                            val userInfo = userResult.data
                            // 注意：这里不要覆盖user_id，因为我们已经从callback获得了正确的user_id
                            // 但是我们需要设置认证状态，所以使用现有的user_id
                            val currentUserId = callbackResponse.userId
                            userPreferences.saveUserInfo(currentUserId, userInfo.email, userInfo.nickname ?: userInfo.username)

                            // 创建User对象用于状态更新
                            val user = User(
                                userId = currentUserId,
                                username = userInfo.username,
                                email = userInfo.email,
                                phone = "",
                                nickname = userInfo.nickname ?: "",
                                status = 1,
                                createdAt = "",
                                updatedAt = ""
                            )
                            updateAuthState(isAuthenticated = true, user = user, isLoading = false)
                            Log.i(TAG, "✅ 登录成功，user_id: ${callbackResponse.userId}")
                            NetworkResult.Success(Unit)
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "❌ 获取用户信息失败", userResult.exception)
                            clearAuthCredentials()
                            NetworkResult.Error(userResult.exception)
                        }
                        else -> {
                            NetworkResult.Error(Exception("获取用户信息失败"))
                        }
                    }
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "❌ OAuth回调处理失败", callbackResult.exception)
                    clearAuthCredentials()
                    NetworkResult.Error(callbackResult.exception)
                }
                else -> {
                    Log.e(TAG, "❌ OAuth回调处理失败：未知错误")
                    NetworkResult.Error(Exception("OAuth回调处理失败"))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 处理授权回调失败", e)
            updateAuthState(isLoading = false, error = e.message)
            NetworkResult.Error(e)
        }
    }
    
    /**
     * 交换授权码获取令牌
     */
    private suspend fun exchangeCodeForTokens(code: String): TokenResponse {
        val codeVerifier = userPreferences.getCodeVerifier().first()
            ?: throw IllegalStateException("Code verifier not found")
        
        val requestBody = mapOf(
            "grant_type" to "authorization_code",
            "client_id" to LogtoConfig.CLIENT_ID,
            "code" to code,
            "redirect_uri" to LogtoConfig.REDIRECT_URI,
            "code_verifier" to codeVerifier
        )
        
        val json = Json.encodeToString(kotlinx.serialization.serializer(), requestBody)
        val body = json.toRequestBody("application/json".toMediaType())
        
        val request = Request.Builder()
            .url("${LogtoConfig.LOGTO_ENDPOINT}/oidc/token")
            .post(body)
            .build()
        
        val response = okHttpClient.newCall(request).execute()
        if (!response.isSuccessful) {
            throw Exception("Token exchange failed: ${response.code}")
        }
        
        val responseBody = response.body?.string() ?: throw Exception("Empty response body")
        return Json.decodeFromString(TokenResponse.serializer(), responseBody)
    }
    
    /**
     * 获取当前有效的访问令牌（仅使用Logto SDK内部管理）
     */
    suspend fun getCurrentAccessToken(): String? {
        Log.i(TAG, "🔄 获取当前访问令牌")

        return try {
            // 直接使用Logto SDK获取当前有效的访问令牌
            val sdkToken = logtoManager.getAccessToken()

            if (!sdkToken.isNullOrEmpty()) {
                Log.i(TAG, "✅ Logto SDK令牌获取成功: ${sdkToken.take(20)}...")

                // 立即保存到完整用户信息中
                userPreferences.updateTokenInfo(
                    accessToken = sdkToken,
                    expiresIn = 3600 // 默认1小时过期
                )

                // 立即更新当前认证状态，确保所有API调用使用新token
                updateAuthState(
                    isAuthenticated = true,
                    isLoading = false,
                    accessToken = sdkToken
                )

                // 通知NetworkManager token已更新
                notifyTokenUpdated(sdkToken)

                return sdkToken
            } else {
                Log.w(TAG, "⚠️ Logto SDK未返回有效令牌")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取访问令牌失败: ${e.message}", e)
            return null
        }
    }

    /**
     * 处理OAuth回调，获取后端user_id
     */
    private suspend fun handleOAuthCallback(code: String, state: String): NetworkResult<OAuthCallbackResponse> {
        return safeApiCall {
            apiService.handleOAuthCallback(code, state)
        }
    }



    /**
     * 获取用户资料
     */
    private suspend fun getUserProfile(): NetworkResult<UserInfoResponse> {
        return safeApiCall {
            apiService.getUserProfile()
        }
    }
    
    /**
     * 加载用户资料
     */
    private suspend fun loadUserProfile() {
        when (val result = getUserProfile()) {
            is NetworkResult.Success -> {
                val userInfo = result.data
                // 将UserInfoResponse转换为User对象
                val user = User(
                    userId = userInfo.userId,
                    username = userInfo.username,
                    email = userInfo.email,
                    phone = "",
                    nickname = userInfo.nickname ?: "",
                    status = 1,
                    createdAt = "",
                    updatedAt = ""
                )
                updateAuthState(user = user)
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "加载用户资料失败", result.exception)
            }
            else -> {}
        }
    }
    
    /**
     * 登出
     */
    suspend fun logout() {
        Log.i(TAG, "🚪 用户登出")
        clearAuthCredentials()
        updateAuthState(isAuthenticated = false, user = null)
    }
    
    /**
     * 清除认证凭据
     */
    private suspend fun clearAuthCredentials() {
        // 清除完整用户信息中的认证部分（但保留用户偏好设置）
        userPreferences.clearAuthInfoOnly()

        // 为了向后兼容，也清除旧的存储方式
        userPreferences.clearAuthCredentials()

        // 通知所有监听器token已清除
        notifyTokenCleared()
    }
    
    /**
     * 更新认证状态
     */
    private fun updateAuthState(
        isAuthenticated: Boolean? = null,
        isLoading: Boolean? = null,
        error: String? = null,
        user: User? = null,
        accessToken: String? = null
    ) {
        _authState.value = _authState.value.copy(
            isAuthenticated = isAuthenticated ?: _authState.value.isAuthenticated,
            isLoading = isLoading ?: _authState.value.isLoading,
            error = error,
            user = user ?: _authState.value.user,
            accessToken = accessToken ?: _authState.value.accessToken
        )
    }

    /**
     * 添加Token更新监听器
     */
    fun addTokenUpdateListener(listener: TokenUpdateListener) {
        synchronized(tokenUpdateListeners) {
            tokenUpdateListeners.add(listener)
        }
        Log.d(TAG, "✅ 添加Token更新监听器")
    }

    /**
     * 移除Token更新监听器
     */
    fun removeTokenUpdateListener(listener: TokenUpdateListener) {
        synchronized(tokenUpdateListeners) {
            tokenUpdateListeners.remove(listener)
        }
        Log.d(TAG, "🗑️ 移除Token更新监听器")
    }

    /**
     * 通知所有监听器token已更新
     */
    private fun notifyTokenUpdated(newToken: String) {
        Log.i(TAG, "🔄 通知所有监听器使用新token: ${newToken.take(20)}...")
        synchronized(tokenUpdateListeners) {
            tokenUpdateListeners.forEach { listener ->
                try {
                    listener.onTokenUpdated(newToken)
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 通知Token更新监听器失败", e)
                }
            }
        }
    }

    /**
     * 通知所有监听器token已清除
     */
    private fun notifyTokenCleared() {
        Log.i(TAG, "🗑️ 通知所有监听器token已清除")
        synchronized(tokenUpdateListeners) {
            tokenUpdateListeners.forEach { listener ->
                try {
                    listener.onTokenCleared()
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 通知Token清除监听器失败", e)
                }
            }
        }
    }
    
    /**
     * 刷新认证状态
     */
    suspend fun refreshAuthState() {
        val isAuthenticated = userPreferences.isAuthenticated().first()
        updateAuthState(isAuthenticated = isAuthenticated)
        if (isAuthenticated) {
            loadUserProfile()
        }
    }
    
    // MARK: - Helper Methods
    
    private fun generateCodeVerifier(): String {
        val bytes = ByteArray(32)
        java.security.SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
    
    private fun generateCodeChallenge(codeVerifier: String): String {
        val digest = java.security.MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(codeVerifier.toByteArray())
        return Base64.getUrlEncoder().withoutPadding().encodeToString(hash)
    }

    /**
     * 原生Logto登录方法
     */
    suspend fun signInNative(activity: android.app.Activity): Result<Unit> {
        return try {
            Log.i(TAG, "🚀 开始原生Logto登录")
            updateAuthState(isLoading = true, error = null)

            // 验证配置
            if (!com.cabycare.android.debug.LogtoDebugHelper.checkConfiguration()) {
                val error = "Logto配置验证失败"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                return Result.failure(Exception(error))
            }

            Log.i(TAG, "📱 调用Logto SDK登录")
            logtoManager.signIn(activity)

            Log.i(TAG, "🔑 获取访问令牌")
            // 登录成功后获取令牌并保存
            val accessToken = logtoManager.getAccessToken()
            val idToken = logtoManager.getIdToken()
            val refreshToken = logtoManager.getRefreshToken()

            Log.d(TAG, "访问令牌: ${if (accessToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "ID令牌: ${if (idToken != null) "已获取" else "未获取"}")
            Log.d(TAG, "刷新令牌: ${if (refreshToken != null) "已获取" else "未获取"}")

            if (accessToken != null) {
                Log.i(TAG, "👤 获取Logto用户信息")
                // 获取Logto用户信息
                val userInfo = logtoManager.getUserInfo()

                // 创建完整用户信息对象
                val completeUserInfo = com.cabycare.android.data.model.CompleteUserInfo(
                    // 基本用户信息
                    logtoId = userInfo?.get("sub") as? String,
                    email = userInfo?.get("email") as? String,
                    username = userInfo?.get("name") as? String,
                    nickname = userInfo?.get("name") as? String,

                    // 认证令牌信息
                    accessToken = accessToken,
                    refreshToken = refreshToken,
                    idToken = idToken,
                    tokenType = "Bearer",
                    expiresIn = 3600, // 默认1小时过期
                    scope = "openid profile email offline_access",
                    tokenIssuedAt = System.currentTimeMillis() / 1000,

                    // Logto用户详细信息
                    logtoUserInfo = userInfo?.let { info ->
                        com.cabycare.android.data.model.LogtoUserInfo(
                            sub = info["sub"] as? String ?: "",
                            name = info["name"] as? String,
                            email = info["email"] as? String,
                            emailVerified = true,
                            updatedAt = System.currentTimeMillis() / 1000
                        )
                    },

                    // 用户状态
                    isAuthenticated = true,
                    lastLoginTime = System.currentTimeMillis(),

                    // 时间戳
                    createdAt = java.time.Instant.now().toString(),
                    updatedAt = java.time.Instant.now().toString()
                )

                Log.i(TAG, "💾 保存完整用户信息到本地存储")
                userPreferences.saveCompleteUserInfo(completeUserInfo)

                // 为了向后兼容，也保存到旧的存储方式
                userPreferences.saveAccessToken(accessToken)
                idToken?.let { userPreferences.saveIdToken(it) }
                refreshToken?.let { userPreferences.saveRefreshToken(it) }

                completeUserInfo.logtoId?.let {
                    userPreferences.saveLogtoId(it)
                    Log.i(TAG, "✅ 保存Logto ID: $it")

                    // 🔑 关键修复：调用后端API获取user_id
                    Log.i(TAG, "🔄 调用后端用户信息API获取user_id...")
                    try {
                        // 调用 /api/user/info 获取用户信息，包含正确的user_id
                        val userInfoResult = getUserProfile()
                        when (userInfoResult) {
                            is NetworkResult.Success -> {
                                val userInfo = userInfoResult.data
                                Log.i(TAG, "✅ 获取到后端user_id: ${userInfo.userId}")

                                // 保存正确的user_id和用户信息
                                userPreferences.saveUserInfo(userInfo.userId, userInfo.email, userInfo.nickname ?: userInfo.username)
                            }
                            is NetworkResult.Error -> {
                                Log.e(TAG, "❌ 获取用户信息失败: ${userInfoResult.exception.message}")
                                // 如果获取用户信息失败，我们暂时使用logto_id作为fallback
                                userPreferences.saveUserId(it)
                                userPreferences.saveUserInfo(it, completeUserInfo.email ?: "", completeUserInfo.username ?: "")
                            }
                            else -> {
                                Log.w(TAG, "⚠️ 获取用户信息返回未知结果")
                                userPreferences.saveUserId(it)
                                userPreferences.saveUserInfo(it, completeUserInfo.email ?: "", completeUserInfo.username ?: "")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 获取用户信息异常: ${e.message}")
                        // 异常情况下使用logto_id作为fallback
                        userPreferences.saveUserId(it)
                        userPreferences.saveUserInfo(it, completeUserInfo.email ?: "", completeUserInfo.username ?: "")
                    }
                }

                completeUserInfo.email?.let {
                    userPreferences.saveUserEmail(it)
                    Log.i(TAG, "✅ 保存用户邮箱: $it")
                }

                completeUserInfo.username?.let {
                    userPreferences.saveUserName(it)
                    Log.i(TAG, "✅ 保存用户姓名: $it")
                }

                // 立即更新认证状态，确保所有API调用使用新token
                updateAuthState(
                    isAuthenticated = true,
                    isLoading = false,
                    accessToken = accessToken
                )

                // 通知NetworkManager token已更新
                notifyTokenUpdated(accessToken)

                Log.i(TAG, "✅ 原生登录成功")
                Result.success(Unit)
            } else {
                val error = "无法获取访问令牌"
                Log.e(TAG, "❌ $error")
                updateAuthState(isAuthenticated = false, isLoading = false, error = error)
                Result.failure(Exception(error))
            }
        } catch (e: Exception) {
            val errorMsg = "原生登录失败: ${e.message}"
            Log.e(TAG, "❌ $errorMsg", e)
            updateAuthState(isAuthenticated = false, isLoading = false, error = e.message)
            Result.failure(e)
        }
    }

    /**
     * 登出方法
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            updateAuthState(isLoading = true)

            // 使用Logto SDK登出
            logtoManager.signOut()

            // 清除本地存储的认证信息
            clearAuthCredentials()

            Log.i(TAG, "✅ 登出成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登出失败", e)
            // 即使登出失败，也清除本地认证信息
            clearAuthCredentials()
            Result.failure(e)
        }
    }


}

/**
 * 令牌响应模型
 */
@kotlinx.serialization.Serializable
data class TokenResponse(
    @kotlinx.serialization.SerialName("access_token")
    val accessToken: String,
    @kotlinx.serialization.SerialName("refresh_token")
    val refreshToken: String? = null,
    @kotlinx.serialization.SerialName("token_type")
    val tokenType: String = "Bearer",
    @kotlinx.serialization.SerialName("expires_in")
    val expiresIn: Long? = null
)
