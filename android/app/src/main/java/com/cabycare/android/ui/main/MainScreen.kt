package com.cabycare.android.ui.main

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import java.net.URLEncoder
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import com.cabycare.android.ui.animals.AnimalsScreen
import com.cabycare.android.ui.care.CareScreen
import com.cabycare.android.ui.device.DeviceScreen
import com.cabycare.android.ui.device.DeviceDetailScreen
import com.cabycare.android.ui.home.HomeScreen

/**
 * 底部导航项目
 */
sealed class BottomNavItem(
    val route: String,
    val title: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector
) {
    object Home : BottomNavItem(
        route = "home",
        title = "首页",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home
    )
    
    object Care : BottomNavItem(
        route = "care",
        title = "关爱",
        selectedIcon = Icons.Filled.Favorite,
        unselectedIcon = Icons.Outlined.FavoriteBorder
    )
    
    object Animals : BottomNavItem(
        route = "animals",
        title = "宠物",
        selectedIcon = Icons.Filled.Pets,
        unselectedIcon = Icons.Outlined.Pets
    )
    
    object Device : BottomNavItem(
        route = "device",
        title = "设备",
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    )
}

/**
 * 主界面
 * 包含底部导航和各个主要页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    
    val bottomNavItems = listOf(
        BottomNavItem.Home,
        BottomNavItem.Care,
        BottomNavItem.Animals,
        BottomNavItem.Device
    )
    
    Scaffold(
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    val selected = currentDestination?.hierarchy?.any { 
                        it.route == item.route 
                    } == true
                    
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = if (selected) item.selectedIcon else item.unselectedIcon,
                                contentDescription = item.title
                            )
                        },
                        label = { Text(item.title) },
                        selected = selected,
                        onClick = {
                            navController.navigate(item.route) {
                                // 避免重复导航到同一个目的地
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = BottomNavItem.Home.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(BottomNavItem.Home.route) {
                HomeScreen()
            }

            composable(BottomNavItem.Care.route) {
                CareScreen()
            }

            composable(BottomNavItem.Animals.route) {
                AnimalsScreen()
            }

            composable(BottomNavItem.Device.route) {
                DeviceScreen(
                    onNavigateToDeviceDetail = { deviceId, deviceName ->
                        val encodedDeviceName = URLEncoder.encode(deviceName, StandardCharsets.UTF_8.toString())
                        navController.navigate("device_detail/$deviceId/$encodedDeviceName")
                    }
                )
            }

            composable("device_detail/{deviceId}/{deviceName}") { backStackEntry ->
                val deviceId = backStackEntry.arguments?.getString("deviceId") ?: ""
                val encodedDeviceName = backStackEntry.arguments?.getString("deviceName") ?: ""
                val deviceName = URLDecoder.decode(encodedDeviceName, StandardCharsets.UTF_8.toString())
                DeviceDetailScreen(
                    deviceId = deviceId,
                    deviceName = deviceName,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}
