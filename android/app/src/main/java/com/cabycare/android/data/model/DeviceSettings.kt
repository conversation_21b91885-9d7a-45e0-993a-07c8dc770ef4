package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 设备OTA设置 - 修改为匹配实际API响应
 */
@Serializable
data class DeviceOTASettings(
    @SerialName("device_id") val deviceId: String? = null,
    @SerialName("auto_ota_enabled") val autoOtaEnabled: Boolean? = null,
    @SerialName("auto_ota_upgrade") val autoOtaUpgrade: String? = null, // 实际API字段
    @SerialName("ota_time_window") val otaTimeWindow: OTATimeWindow? = null,
    @SerialName("beta_updates") val betaUpdates: Boolean? = null,
    @SerialName("last_check") val lastCheck: String? = null,
    @SerialName("next_check") val nextCheck: String? = null,
    @SerialName("idle_update_start_hour") val idleUpdateStartHour: Int? = null,
    @SerialName("idle_update_end_hour") val idleUpdateEndHour: Int? = null
) {
    // 计算属性：从auto_ota_upgrade字段获取布尔值
    val isAutoOtaEnabled: Boolean
        get() = autoOtaEnabled ?: (autoOtaUpgrade == "on")
}

/**
 * OTA时间窗口
 */
@Serializable
data class OTATimeWindow(
    val start: String,
    val end: String
) {
    companion object {
        val defaultWindow = OTATimeWindow("02:00", "04:00")
    }
}

/**
 * 简单的OTA设置格式（实际API返回）
 */
@Serializable
data class SimpleOTASettings(
    @SerialName("auto_ota_upgrade") val autoOtaUpgrade: String
)

/**
 * OTA设置更新请求
 */
@Serializable
data class DeviceOTAUpdateRequest(
    @SerialName("auto_ota_enabled") val autoOtaEnabled: Boolean,
    @SerialName("ota_time_window") val otaTimeWindow: OTATimeWindow,
    @SerialName("beta_updates") val betaUpdates: Boolean,
    @SerialName("idle_update_start_hour") val idleUpdateStartHour: Int? = null,
    @SerialName("idle_update_end_hour") val idleUpdateEndHour: Int? = null
)

/**
 * 简单的OTA更新请求（匹配实际API格式）
 */
@Serializable
data class SimpleOTAUpdateRequest(
    @SerialName("auto_ota_upgrade") val autoOtaUpgrade: String,
    @SerialName("idle_update_start_hour") val idleUpdateStartHour: Int? = null,
    @SerialName("idle_update_end_hour") val idleUpdateEndHour: Int? = null
) {
    constructor(enabled: Boolean, idleUpdateStartHour: Int? = null, idleUpdateEndHour: Int? = null) : this(
        autoOtaUpgrade = if (enabled) "on" else "off",
        idleUpdateStartHour = idleUpdateStartHour,
        idleUpdateEndHour = idleUpdateEndHour
    )
}

/**
 * API响应包装
 */
@Serializable
data class OTASettingsResponse(
    val status: String,
    val message: String,
    val data: DeviceOTASettings
)



/**
 * 传感器类型
 */
enum class SensorType(val value: String) {
    CAMERA("camera"),
    WEIGHT_SENSOR("weight_sensor"),
    WIFI("wifi"),
    MICROPHONE("microphone"),
    BLUETOOTH("bluetooth"),
    TEMPERATURE_HUMIDITY_SENSOR("temperature_humidity_sensor");

    val displayName: String
        get() = when (this) {
            CAMERA -> "相机"
            WEIGHT_SENSOR -> "重量传感器"
            WIFI -> "WiFi"
            MICROPHONE -> "麦克风"
            BLUETOOTH -> "蓝牙"
            TEMPERATURE_HUMIDITY_SENSOR -> "温湿度传感器"
        }
}

/**
 * 传感器错误信息
 */
data class SensorError(
    val sensorType: SensorType,
    val errorType: String?,
    val errorTime: String?
) {
    val isError: Boolean
        get() = errorType != null && errorType.isNotEmpty() && errorType != "0"

    val errorMessage: String
        get() = if (isError) {
            "传感器异常 (错误类型: ${errorType ?: "未知"})"
        } else {
            "传感器正常"
        }
}

/**
 * 设备传感器状态响应 - 修改为匹配实际API响应
 */
@Serializable
data class DeviceSensorStatusResponse(
    @SerialName("device_id") val deviceId: String? = null,
    @SerialName("camera_last_error_type") val cameraLastErrorType: String? = null,
    @SerialName("camera_last_error_time") val cameraLastErrorTime: String? = null,
    @SerialName("weight_sensor_last_error_type") val weightSensorLastErrorType: String? = null,
    @SerialName("weight_sensor_last_error_time") val weightSensorLastErrorTime: String? = null,
    @SerialName("wifi_last_error_type") val wifiLastErrorType: String? = null,
    @SerialName("wifi_last_error_time") val wifiLastErrorTime: String? = null,
    @SerialName("microphone_last_error_type") val microphoneLastErrorType: String? = null,
    @SerialName("microphone_last_error_time") val microphoneLastErrorTime: String? = null,
    @SerialName("bluetooth_last_error_type") val bluetoothLastErrorType: String? = null,
    @SerialName("bluetooth_last_error_time") val bluetoothLastErrorTime: String? = null,
    @SerialName("temperature_humidity_sensor_last_error_type") val temperatureHumiditySensorLastErrorType: String? = null,
    @SerialName("temperature_humidity_sensor_last_error_time") val temperatureHumiditySensorLastErrorTime: String? = null,
    @SerialName("last_updated") val lastUpdated: String? = null
) {
    /**
     * 获取所有传感器状态
     */
    val sensorStatuses: List<SensorError>
        get() = listOf(
            SensorError(SensorType.CAMERA, cameraLastErrorType, cameraLastErrorTime),
            SensorError(SensorType.WEIGHT_SENSOR, weightSensorLastErrorType, weightSensorLastErrorTime),
            SensorError(SensorType.WIFI, wifiLastErrorType, wifiLastErrorTime),
            SensorError(SensorType.MICROPHONE, microphoneLastErrorType, microphoneLastErrorTime),
            SensorError(SensorType.BLUETOOTH, bluetoothLastErrorType, bluetoothLastErrorTime),
            SensorError(SensorType.TEMPERATURE_HUMIDITY_SENSOR, temperatureHumiditySensorLastErrorType, temperatureHumiditySensorLastErrorTime)
        )

    /**
     * 检查是否有传感器错误
     */
    val hasErrors: Boolean
        get() = sensorStatuses.any { it.isError }

    /**
     * 获取错误传感器数量
     */
    val errorCount: Int
        get() = sensorStatuses.count { it.isError }

    /**
     * 获取特定传感器的状态
     */
    fun sensorStatus(type: SensorType): SensorError? {
        return sensorStatuses.find { it.sensorType == type }
    }
}

/**
 * 设备基本信息更新请求
 */
@Serializable
data class DeviceBasicInfoUpdateRequest(
    val name: String? = null
)

/**
 * OTA更新模式
 */
enum class OTAUpdateMode {
    IMMEDIATE, // 立即更新
    IDLE       // 闲时更新
}
