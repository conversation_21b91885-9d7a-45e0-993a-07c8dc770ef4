package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 猫咪性别枚举
 */
enum class CatGender(val value: Int) {
    MALE(0),
    FEMALE(1);
    
    companion object {
        fun fromValue(value: Int): CatGender {
            return values().find { it.value == value } ?: MALE
        }
    }
    
    val displayName: String
        get() = when (this) {
            MALE -> "公猫"
            FEMALE -> "母猫"
        }
}

/**
 * 猫咪类型枚举
 */
enum class CatType(val displayName: String) {
    WHITE("小白"),
    BLACK("小黑"),
    CALICO("三花"),
    TABBY("虎斑"),
    ORANGE("橘猫"),
    OTHER("其他");
    
    companion object {
        fun fromDisplayName(name: String): CatType {
            return values().find { it.displayName == name } ?: OTHER
        }
    }
}

/**
 * 猫咪健康状态枚举
 */
enum class CatHealthStatus(val displayName: String, val colorName: String) {
    HEALTHY("健康", "green"),
    NEEDS_ATTENTION("需要关注", "orange"),
    SICK("生病", "red");
    
    companion object {
        fun fromDisplayName(name: String): CatHealthStatus {
            return values().find { it.displayName == name } ?: HEALTHY
        }
    }
}

/**
 * 猫咪活动水平枚举
 */
enum class CatActivityLevel(val displayName: String) {
    LOW("低"),
    NORMAL("正常"),
    HIGH("高"),
    VERY_HIGH("很高");
    
    companion object {
        fun fromDisplayName(name: String): CatActivityLevel {
            return values().find { it.displayName == name } ?: NORMAL
        }
    }
}

/**
 * 疫苗接种记录
 */
@Serializable
data class Vaccination(
    val id: String,
    val name: String,
    val date: String,
    val nextDueDate: String? = null,
    val veterinarian: String? = null,
    val notes: String? = null
)

/**
 * 药物记录
 */
@Serializable
data class Medication(
    val id: String,
    val name: String,
    val dosage: String,
    val frequency: String,
    val startDate: String,
    val endDate: String? = null,
    val notes: String? = null
)

/**
 * 猫咪档案模型
 * 对应Swift版本的CatProfile模型
 */
@Serializable
data class CatProfile(
    val id: String,
    val name: String,
    val age: String,
    val gender: Int, // 0: 公猫, 1: 母猫
    val weight: String,
    val healthStatus: String = CatHealthStatus.HEALTHY.displayName,
    val activityLevel: String = CatActivityLevel.NORMAL.displayName,
    val type: String = CatType.OTHER.displayName,
    val neuteredStatus: String? = null, // 绝育状态
    @SerialName("avatar_url")
    val avatarUrl: String? = null,
    @SerialName("birthday")
    val birthDate: String? = null,
    val lastCheckupDate: String? = null,
    val vaccinations: List<Vaccination> = emptyList(),
    val medications: List<Medication> = emptyList(),
    val dietaryRestrictions: List<String> = emptyList()
) {
    /**
     * 获取性别枚举
     */
    val genderEnum: CatGender
        get() = CatGender.fromValue(gender)
    
    /**
     * 获取健康状态枚举
     */
    val healthStatusEnum: CatHealthStatus
        get() = CatHealthStatus.fromDisplayName(healthStatus)
    
    /**
     * 获取活动水平枚举
     */
    val activityLevelEnum: CatActivityLevel
        get() = CatActivityLevel.fromDisplayName(activityLevel)
    
    /**
     * 获取猫咪类型枚举
     */
    val typeEnum: CatType
        get() = CatType.fromDisplayName(type)
    
    /**
     * 计算正确的年龄显示
     */
    val formattedAge: String
        get() {
            return if (birthDate != null) {
                calculateAgeFromBirthDate(birthDate)
            } else {
                age // 使用原始的age字段作为fallback
            }
        }

    /**
     * 是否需要关注
     */
    val needsAttention: Boolean
        get() = healthStatusEnum != CatHealthStatus.HEALTHY
}

/**
 * 猫咪API响应模型
 * 对应Swift版本的CatAPIResponse模型
 */
@Serializable
data class CatAPIResponse(
    @SerialName("cat_id")
    val catId: String,
    @SerialName("user_id")
    val userId: String,
    val name: String,
    val birthday: String? = null,
    val gender: Int,
    val color: String? = null,
    val weight: Double,
    val status: Int,
    @SerialName("avatar_url")
    val avatarUrl: String? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 转换为CatProfile模型
     */
    fun toCatProfile(): CatProfile {
        return CatProfile(
            id = catId,
            name = name,
            age = calculateAge(birthday),
            gender = gender,
            weight = "${weight}kg",
            healthStatus = CatHealthStatus.HEALTHY.displayName,
            activityLevel = CatActivityLevel.NORMAL.displayName,
            type = CatType.WHITE.displayName,
            neuteredStatus = null,
            avatarUrl = avatarUrl,
            birthDate = birthday,
            lastCheckupDate = null,
            vaccinations = emptyList(),
            medications = emptyList(),
            dietaryRestrictions = emptyList()
        )
    }
    
    private fun calculateAge(birthday: String?): String {
        return birthday?.let {
            calculateAgeFromBirthDate(it)
        } ?: "未知年龄"
    }
}

/**
 * 创建猫咪API响应
 * 对应Swift版本的CreateCatAPIResponse
 */
@Serializable
data class CreateCatAPIResponse(
    val status: String,
    val message: String,
    val data: CatAPIResponse
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == "success"
}

/**
 * 猫咪统计数据模型 - 用于首页显示
 */
@Serializable
data class CatStatistics(
    @SerialName("cat_id") val catId: String,
    @SerialName("cat_name") val catName: String,
    @SerialName("avatar_url") val avatarUrl: String? = null,
    @SerialName("recent_toilet_count") val recentToiletCount: Int,
    @SerialName("recent_total_duration") val recentTotalDuration: Long, // 秒
    @SerialName("previous_toilet_count") val previousToiletCount: Int,
    @SerialName("previous_total_duration") val previousTotalDuration: Long, // 秒
    @SerialName("current_weight") val currentWeight: Double? = null,
    @SerialName("previous_weight") val previousWeight: Double? = null,
    @SerialName("weight_change") val weightChange: Double? = null,
    @SerialName("weight_change_percentage") val weightChangePercentage: Double? = null
) {
    /**
     * 如厕次数对比结果
     */
    val countComparison: ComparisonResult
        get() = when {
            recentToiletCount > previousToiletCount -> ComparisonResult.INCREASED
            recentToiletCount < previousToiletCount -> ComparisonResult.DECREASED
            else -> ComparisonResult.SAME
        }

    /**
     * 格式化的如厕时长（分钟）
     */
    val formattedDuration: String
        get() {
            val minutes = recentTotalDuration / 60
            return "${minutes}分钟"
        }

    /**
     * 格式化的体重变化百分比
     */
    val formattedWeightChange: String?
        get() {
            return weightChangePercentage?.let { percentage ->
                val sign = if (percentage >= 0) "+" else ""
                "$sign${String.format("%.1f", percentage)}%"
            }
        }
}

/**
 * 猫咪警告信息模型
 */
@Serializable
data class CatAlert(
    @SerialName("cat_id") val catId: String,
    @SerialName("cat_name") val catName: String,
    @SerialName("avatar_url") val avatarUrl: String? = null,
    @SerialName("last_toilet_time") val lastToiletTime: String? = null,
    @SerialName("hours_without_toilet") val hoursWithoutToilet: Int? = null
) {
    /**
     * 警告消息
     */
    val alertMessage: String
        get() = hoursWithoutToilet?.let { hours ->
            "${catName} 已经 ${hours} 小时没有如厕了"
        } ?: "${catName} 暂无如厕记录"
}

/**
 * 对比结果枚举
 */
enum class ComparisonResult {
    INCREASED,
    DECREASED,
    SAME;

    val displayText: String
        get() = when (this) {
            INCREASED -> "增加"
            DECREASED -> "减少"
            SAME -> "持平"
        }

    val colorResId: Int
        get() = when (this) {
            INCREASED -> android.R.color.holo_green_dark
            DECREASED -> android.R.color.holo_orange_dark
            SAME -> android.R.color.darker_gray
        }
}

/**
 * 根据出生日期计算年龄
 * 支持多种日期格式：
 * - ISO 8601: "2020-01-01T00:00:00Z"
 * - 简单格式: "2020-01-01"
 */
fun calculateAgeFromBirthDate(birthDate: String): String {
    return try {
        // 处理ISO 8601格式，提取日期部分
        val dateOnly = if (birthDate.contains("T")) {
            birthDate.split("T")[0]
        } else {
            birthDate
        }

        // 解析日期格式 "yyyy-MM-dd"
        val birthParts = dateOnly.split("-")
        if (birthParts.size != 3) return "未知年龄"

        val birthYear = birthParts[0].toInt()
        val birthMonth = birthParts[1].toInt()
        val birthDay = birthParts[2].toInt()

        // 获取当前日期
        val calendar = java.util.Calendar.getInstance()
        val currentYear = calendar.get(java.util.Calendar.YEAR)
        val currentMonth = calendar.get(java.util.Calendar.MONTH) + 1 // Calendar.MONTH 从0开始
        val currentDay = calendar.get(java.util.Calendar.DAY_OF_MONTH)

        // 计算年龄
        var ageYears = currentYear - birthYear
        var ageMonths = currentMonth - birthMonth
        var ageDays = currentDay - birthDay

        // 调整月份和年份
        if (ageDays < 0) {
            ageMonths--
            // 获取上个月的天数来计算剩余天数
            val prevMonth = if (currentMonth == 1) 12 else currentMonth - 1
            val prevYear = if (currentMonth == 1) currentYear - 1 else currentYear
            val daysInPrevMonth = getDaysInMonth(prevYear, prevMonth)
            ageDays += daysInPrevMonth
        }

        if (ageMonths < 0) {
            ageYears--
            ageMonths += 12
        }

        // 格式化年龄显示
        when {
            ageYears > 0 -> {
                if (ageMonths > 0) {
                    "${ageYears}岁${ageMonths}个月"
                } else {
                    "${ageYears}岁"
                }
            }
            ageMonths > 0 -> "${ageMonths}个月"
            ageDays > 0 -> "${ageDays}天"
            else -> "今天出生"
        }
    } catch (e: Exception) {
        "未知年龄"
    }
}

/**
 * 获取指定年月的天数
 */
private fun getDaysInMonth(year: Int, month: Int): Int {
    val calendar = java.util.Calendar.getInstance()
    calendar.set(year, month - 1, 1) // Calendar.MONTH 从0开始
    return calendar.getActualMaximum(java.util.Calendar.DAY_OF_MONTH)
}

/**
 * 更新猫咪API响应
 * 对应Swift版本的UpdateCatAPIResponse
 */
@Serializable
data class UpdateCatAPIResponse(
    @SerialName("cat_id") val catId: String,
    val name: String,
    val status: Int,
    @SerialName("updated_at") val updatedAt: String
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = status == 1
}
