package com.cabycare.android.ui.device

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.cabycare.android.data.model.*

/**
 * 设备详情页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceDetailScreen(
    deviceId: String,
    deviceName: String,
    onNavigateBack: () -> Unit,
    viewModel: DeviceDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    var isEditing by remember { mutableStateOf(false) }
    var isEditingOTA by remember { mutableStateOf(false) }
    var editedName by remember { mutableStateOf(deviceName) }
    
    // OTA编辑状态
    var autoOtaEnabled by remember { mutableStateOf(false) }
    var updateMode by remember { mutableStateOf(OTAUpdateMode.IDLE) }
    var idleUpdateStartHour by remember { mutableStateOf(2) }
    var idleUpdateEndHour by remember { mutableStateOf(4) }

    // 启动时加载设备详情
    LaunchedEffect(deviceId) {
        viewModel.loadDeviceDetail(deviceId)
    }

    // 处理成功状态
    LaunchedEffect(uiState.showSuccess) {
        if (uiState.showSuccess) {
            isEditing = false
            viewModel.clearSuccess()
        }
    }

    LaunchedEffect(uiState.showOTASuccess) {
        if (uiState.showOTASuccess) {
            isEditingOTA = false
            viewModel.clearSuccess()
        }
    }

    // 错误对话框
    uiState.error?.let { message ->
        AlertDialog(
            onDismissRequest = { viewModel.clearError() },
            title = { Text("错误") },
            text = { Text(message) },
            confirmButton = {
                TextButton(onClick = { viewModel.clearError() }) {
                    Text("确定")
                }
            }
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设备详情") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    if (!isEditing && !isEditingOTA) {
                        IconButton(onClick = { isEditing = true }) {
                            Icon(Icons.Default.Edit, contentDescription = "编辑")
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        if (uiState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 设备基本信息
                item {
                    DeviceBasicInfoSection(
                        deviceStatus = uiState.deviceStatus,
                        sensorStatus = uiState.sensorStatus,
                        isEditing = isEditing,
                        editedName = editedName,
                        onNameChange = { editedName = it },
                        onSave = {
                            viewModel.updateDeviceBasicInfo(deviceId, editedName)
                        },
                        onCancel = {
                            isEditing = false
                            editedName = uiState.deviceStatus?.name ?: deviceName
                        },
                        isSaving = uiState.isSaving,
                        onRefresh = { viewModel.refreshDeviceStatus(deviceId) },
                        isRefreshing = uiState.isRefreshingStatus
                    )
                }

                // OTA状态
                item {
                    OTAStatusSection(
                        otaStatus = uiState.otaStatus
                    )
                }

                // OTA设置
                item {
                    OTASettingsSection(
                        otaSettings = uiState.otaSettings,
                        isEditing = isEditingOTA,
                        autoOtaEnabled = autoOtaEnabled,
                        updateMode = updateMode,
                        idleUpdateStartHour = idleUpdateStartHour,
                        idleUpdateEndHour = idleUpdateEndHour,
                        onAutoOtaEnabledChange = { autoOtaEnabled = it },
                        onUpdateModeChange = { updateMode = it },
                        onIdleUpdateStartHourChange = { idleUpdateStartHour = it },
                        onIdleUpdateEndHourChange = { idleUpdateEndHour = it },
                        onEdit = {
                            isEditingOTA = true
                            // 设置初始值
                            uiState.otaSettings?.let { settings ->
                                autoOtaEnabled = settings.isAutoOtaEnabled
                                idleUpdateStartHour = settings.idleUpdateStartHour ?: 2
                                idleUpdateEndHour = settings.idleUpdateEndHour ?: 4
                            }
                        },
                        onSave = {
                            val actualStartHour: Int?
                            val actualEndHour: Int?
                            
                            if (!autoOtaEnabled) {
                                actualStartHour = null
                                actualEndHour = null
                            } else if (updateMode == OTAUpdateMode.IMMEDIATE) {
                                val currentHour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
                                actualStartHour = currentHour
                                actualEndHour = currentHour
                            } else {
                                actualStartHour = idleUpdateStartHour
                                actualEndHour = idleUpdateEndHour
                            }
                            
                            viewModel.updateOTASettings(
                                deviceId, 
                                autoOtaEnabled, 
                                actualStartHour, 
                                actualEndHour
                            )
                        },
                        onCancel = { isEditingOTA = false },
                        isSaving = uiState.isSaving,
                        isLoading = uiState.isLoadingOTA
                    )
                }

                // 传感器状态
                item {
                    SensorStatusSection(
                        sensorStatus = uiState.sensorStatus,
                        onRefresh = { viewModel.refreshSensorStatus(deviceId) },
                        isRefreshing = uiState.isRefreshingSensorStatus
                    )
                }
            }
        }
    }
}

/**
 * 设备基本信息区域
 */
@Composable
private fun DeviceBasicInfoSection(
    deviceStatus: DeviceStatusResponse?,
    sensorStatus: DeviceSensorStatusResponse?,
    isEditing: Boolean,
    editedName: String,
    onNameChange: (String) -> Unit,
    onSave: () -> Unit,
    onCancel: () -> Unit,
    isSaving: Boolean,
    onRefresh: () -> Unit,
    isRefreshing: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "设备信息",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                if (!isEditing) {
                    IconButton(onClick = onRefresh) {
                        if (isRefreshing) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp
                            )
                        } else {
                            Icon(Icons.Default.Refresh, contentDescription = "刷新状态")
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            deviceStatus?.let { status ->
                if (isEditing) {
                    // 编辑模式
                    OutlinedTextField(
                        value = editedName,
                        onValueChange = onNameChange,
                        label = { Text("设备名称") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = onSave,
                            enabled = !isSaving,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (isSaving) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(16.dp),
                                    strokeWidth = 2.dp,
                                    color = MaterialTheme.colorScheme.onPrimary
                                )
                            } else {
                                Text("保存")
                            }
                        }
                        
                        OutlinedButton(
                            onClick = onCancel,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("取消")
                        }
                    }
                } else {
                    // 显示模式
                    DetailRow("设备名称", status.name)
                    DetailRow("设备型号", status.model ?: "未知")
                    DetailRow("固件版本", status.firmwareVersion ?: "未知")
                    
                    // 传感器状态显示
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "传感器状态",
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                        Spacer(modifier = Modifier.weight(1f))

                        sensorStatus?.let { sensors ->
                            if (sensors.hasErrors) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Warning,
                                        contentDescription = "异常",
                                        tint = Color.Red,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Text(
                                        text = "${sensors.errorCount}个异常",
                                        fontWeight = FontWeight.Medium,
                                        color = Color.Red
                                    )
                                }
                            } else {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.CheckCircle,
                                        contentDescription = "正常",
                                        tint = Color.Green,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Text(
                                        text = "全部正常",
                                        fontWeight = FontWeight.Medium,
                                        color = Color.Green
                                    )
                                }
                            }
                        } ?: Text(
                            text = "检测中...",
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                        )
                    }
                    
                    status.lastSeen?.let { lastSeen ->
                        DetailRow("最后活动", lastSeen)
                    }
                }
            } ?: run {
                Text(
                    text = "设备信息加载中...",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 详情行组件
 */
@Composable
private fun DetailRow(title: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * OTA状态区域
 */
@Composable
private fun OTAStatusSection(
    otaStatus: DeviceOTAStatusResponse?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "OTA状态",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            otaStatus?.let { status ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 状态图标
                    val statusColor = when (status.otaStatus) {
                        DeviceOTAStatus.IDLE -> Color.Gray
                        DeviceOTAStatus.UPDATING -> Color.Blue
                        DeviceOTAStatus.FAILED -> Color.Red
                        DeviceOTAStatus.COMPLETED -> Color.Green
                    }

                    val statusIcon = when (status.otaStatus) {
                        DeviceOTAStatus.IDLE -> Icons.Default.Circle
                        DeviceOTAStatus.UPDATING -> Icons.Default.Sync
                        DeviceOTAStatus.FAILED -> Icons.Default.Error
                        DeviceOTAStatus.COMPLETED -> Icons.Default.CheckCircle
                    }

                    Icon(
                        imageVector = statusIcon,
                        contentDescription = status.otaStatus.displayName,
                        tint = statusColor,
                        modifier = Modifier.size(20.dp)
                    )

                    Text(
                        text = status.otaStatus.displayName,
                        fontWeight = FontWeight.Medium,
                        color = statusColor
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "最后更新: ${status.lastUpdated}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            } ?: run {
                Text(
                    text = "OTA状态加载中...",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * OTA设置区域
 */
@Composable
private fun OTASettingsSection(
    otaSettings: DeviceOTASettings?,
    isEditing: Boolean,
    autoOtaEnabled: Boolean,
    updateMode: OTAUpdateMode,
    idleUpdateStartHour: Int,
    idleUpdateEndHour: Int,
    onAutoOtaEnabledChange: (Boolean) -> Unit,
    onUpdateModeChange: (OTAUpdateMode) -> Unit,
    onIdleUpdateStartHourChange: (Int) -> Unit,
    onIdleUpdateEndHourChange: (Int) -> Unit,
    onEdit: () -> Unit,
    onSave: () -> Unit,
    onCancel: () -> Unit,
    isSaving: Boolean,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "OTA设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                if (!isEditing && otaSettings != null) {
                    TextButton(onClick = onEdit) {
                        Text("编辑")
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            if (isLoading) {
                CircularProgressIndicator()
            } else {
                otaSettings?.let { settings ->
                    if (isEditing) {
                        // 编辑模式
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Switch(
                                checked = autoOtaEnabled,
                                onCheckedChange = onAutoOtaEnabledChange
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("启用自动OTA升级")
                        }

                        if (autoOtaEnabled) {
                            Spacer(modifier = Modifier.height(12.dp))

                            Text(
                                text = "更新模式",
                                fontWeight = FontWeight.Medium
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Row {
                                FilterChip(
                                    onClick = { onUpdateModeChange(OTAUpdateMode.IMMEDIATE) },
                                    label = { Text("立即更新") },
                                    selected = updateMode == OTAUpdateMode.IMMEDIATE
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                FilterChip(
                                    onClick = { onUpdateModeChange(OTAUpdateMode.IDLE) },
                                    label = { Text("闲时更新") },
                                    selected = updateMode == OTAUpdateMode.IDLE
                                )
                            }

                            if (updateMode == OTAUpdateMode.IDLE) {
                                Spacer(modifier = Modifier.height(12.dp))

                                Text(
                                    text = "闲时更新时间",
                                    fontWeight = FontWeight.Medium
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Text("从")
                                    Text(
                                        text = "${idleUpdateStartHour}:00",
                                        modifier = Modifier
                                            .background(
                                                MaterialTheme.colorScheme.surfaceVariant,
                                                RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                    )
                                    Text("到")
                                    Text(
                                        text = "${idleUpdateEndHour}:00",
                                        modifier = Modifier
                                            .background(
                                                MaterialTheme.colorScheme.surfaceVariant,
                                                RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Button(
                                onClick = onSave,
                                enabled = !isSaving,
                                modifier = Modifier.weight(1f)
                            ) {
                                if (isSaving) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        strokeWidth = 2.dp,
                                        color = MaterialTheme.colorScheme.onPrimary
                                    )
                                } else {
                                    Text("保存")
                                }
                            }

                            OutlinedButton(
                                onClick = onCancel,
                                modifier = Modifier.weight(1f)
                            ) {
                                Text("取消")
                            }
                        }
                    } else {
                        // 显示模式
                        DetailRow("自动升级", if (settings.isAutoOtaEnabled) "已启用" else "已禁用")

                        if (settings.isAutoOtaEnabled) {
                            settings.idleUpdateStartHour?.let { startHour ->
                                settings.idleUpdateEndHour?.let { endHour ->
                                    DetailRow("升级时间", "${startHour}:00 - ${endHour}:00")
                                }
                            }
                        }

                        settings.lastCheck?.let { lastCheck ->
                            DetailRow("最后检查", lastCheck)
                        }

                        settings.nextCheck?.let { nextCheck ->
                            DetailRow("下次检查", nextCheck)
                        }
                    }
                } ?: run {
                    Text(
                        text = "OTA设置加载中...",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 传感器状态区域
 */
@Composable
private fun SensorStatusSection(
    sensorStatus: DeviceSensorStatusResponse?,
    onRefresh: () -> Unit,
    isRefreshing: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "传感器状态",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                IconButton(onClick = onRefresh) {
                    if (isRefreshing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新传感器状态")
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            sensorStatus?.let { status ->
                // 传感器状态总览
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (status.hasErrors) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = "警告",
                            tint = Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                        Text(
                            text = "检测到 ${status.errorCount} 个传感器异常",
                            fontWeight = FontWeight.Medium,
                            color = Color.Red
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "正常",
                            tint = Color.Green,
                            modifier = Modifier.size(20.dp)
                        )
                        Text(
                            text = "所有传感器状态正常",
                            fontWeight = FontWeight.Medium,
                            color = Color.Green
                        )
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 传感器详细状态
                if (status.hasErrors) {
                    // 异常传感器
                    Text(
                        text = "异常传感器",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Red
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    SensorType.values().forEach { sensorType ->
                        status.sensorStatus(sensorType)?.let { sensorError ->
                            if (sensorError.isError) {
                                SensorDetailRow(sensorError = sensorError)
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    // 正常传感器
                    Text(
                        text = "正常传感器",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Green
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    SensorType.values().forEach { sensorType ->
                        status.sensorStatus(sensorType)?.let { sensorError ->
                            if (!sensorError.isError) {
                                SensorDetailRow(sensorError = sensorError)
                            }
                        }
                    }
                } else {
                    // 所有传感器都正常
                    SensorType.values().forEach { sensorType ->
                        SensorDetailRow(sensorType = sensorType, isNormal = true)
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "最后更新: ${status.lastUpdated ?: "未知"}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            } ?: run {
                Text(
                    text = "传感器状态加载中...",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 传感器详细行
 */
@Composable
private fun SensorDetailRow(
    sensorError: SensorError? = null,
    sensorType: SensorType? = null,
    isNormal: Boolean = false
) {
    val actualSensorType = sensorError?.sensorType ?: sensorType!!
    val isError = sensorError?.isError ?: false

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 传感器图标
        val sensorIcon = when (actualSensorType) {
            SensorType.CAMERA -> Icons.Default.Videocam
            SensorType.WEIGHT_SENSOR -> Icons.Default.Scale
            SensorType.WIFI -> Icons.Default.Wifi
            SensorType.MICROPHONE -> Icons.Default.Mic
            SensorType.BLUETOOTH -> Icons.Default.Bluetooth
            SensorType.TEMPERATURE_HUMIDITY_SENSOR -> Icons.Default.Thermostat
        }

        Icon(
            imageVector = sensorIcon,
            contentDescription = actualSensorType.displayName,
            tint = if (isError) Color.Red else Color.Green,
            modifier = Modifier.size(16.dp)
        )

        Text(
            text = actualSensorType.displayName,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = if (isNormal || !isError) "正常" else "异常",
            fontSize = 12.sp,
            color = if (isError) Color.Red else Color.Green,
            fontWeight = FontWeight.Medium
        )
    }
}
