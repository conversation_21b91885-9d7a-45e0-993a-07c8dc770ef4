package com.cabycare.android.data.api

import com.cabycare.android.data.model.CatAlert
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.data.model.CatStatistics
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * 猫咪相关API服务
 */
interface CatApiService {

    /**
     * 获取猫咪统计数据（用于首页）
     */
    @GET("api/cats/statistics")
    suspend fun getCatStatistics(
        @Query("user_id") userId: String
    ): List<CatStatistics>

    /**
     * 获取猫咪警告信息（用于首页）
     */
    @GET("api/cats/alerts")
    suspend fun getCatAlerts(
        @Query("user_id") userId: String
    ): List<CatAlert>

    /**
     * 获取所有猫咪档案（用于宠物管理页面）
     */
    @GET("api/cats")
    suspend fun getAllCats(
        @Query("user_id") userId: String
    ): List<CatProfile>

    /**
     * 获取隐藏的猫咪档案
     */
    @GET("api/cats/hidden")
    suspend fun getHiddenCats(
        @Query("user_id") userId: String
    ): List<CatProfile>

    /**
     * 获取单个猫咪详情
     */
    @GET("api/cats/{cat_id}")
    suspend fun getCatProfile(
        @Query("cat_id") catId: String,
        @Query("user_id") userId: String
    ): CatProfile
}
