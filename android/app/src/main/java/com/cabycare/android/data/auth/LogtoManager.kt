package com.cabycare.android.data.auth

import android.app.Activity
import android.app.Application
import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import io.logto.sdk.android.LogtoClient
import io.logto.sdk.android.type.LogtoConfig
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import com.cabycare.android.data.network.LogtoTokenService
import com.cabycare.android.data.network.LogtoTokenResponse
import com.cabycare.android.data.local.UserPreferences
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Logto SDK管理器
 * 基于iOS版本的NativeLogtoManager.swift
 */
@Singleton
class LogtoManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferences: UserPreferences
) {
    private var logtoClient: LogtoClient? = null
    private val logtoTokenService: LogtoTokenService

    companion object {
        private const val TAG = "LogtoManager"
    }

    // JSON序列化器
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    init {
        initializeLogtoClient()
        logtoTokenService = createLogtoTokenService()
    }
    
    private fun initializeLogtoClient() {
        try {
            val config = LogtoConfig(
                endpoint = com.cabycare.android.data.auth.LogtoConfig.LOGTO_ENDPOINT,
                appId = com.cabycare.android.data.auth.LogtoConfig.CLIENT_ID,
                scopes = listOf("openid", "profile", "email", "offline_access"),
                usingPersistStorage = true
            )

            logtoClient = LogtoClient(config, context.applicationContext as Application)
            Log.i(TAG, "✅ Logto客户端初始化成功")
            Log.d(TAG, "配置详情:")
            Log.d(TAG, "  - Endpoint: ${com.cabycare.android.data.auth.LogtoConfig.LOGTO_ENDPOINT}")
            Log.d(TAG, "  - Client ID: ${com.cabycare.android.data.auth.LogtoConfig.CLIENT_ID}")
            Log.d(TAG, "  - Redirect URI: ${com.cabycare.android.data.auth.LogtoConfig.REDIRECT_URI}")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Logto客户端初始化失败: ${e.message}", e)
        }
    }

    /**
     * 创建Logto Token服务
     */
    private fun createLogtoTokenService(): LogtoTokenService {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d(TAG, "Token API: $message")
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .build()

        val retrofit = Retrofit.Builder()
            .baseUrl(com.cabycare.android.data.auth.LogtoConfig.LOGTO_ENDPOINT + "/")
            .client(okHttpClient)
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .build()

        return retrofit.create(LogtoTokenService::class.java)
    }

    /**
     * 检查是否已认证
     */
    val isAuthenticated: Boolean
        get() = logtoClient?.isAuthenticated == true
    
    /**
     * 获取访问令牌
     */
    suspend fun getAccessToken(): String? = suspendCancellableCoroutine { continuation ->
        logtoClient?.getAccessToken { logtoException, accessToken ->
            if (logtoException != null) {
                Log.e(TAG, "获取访问令牌失败: ${logtoException}")
                continuation.resume(null)
            } else {
                continuation.resume(accessToken?.token)
            }
        }
    }
    
    /**
     * 获取ID令牌
     */
    suspend fun getIdToken(): String? = suspendCancellableCoroutine { continuation ->
        logtoClient?.getIdTokenClaims { logtoException, idTokenClaims ->
            if (logtoException != null) {
                Log.e(TAG, "获取ID令牌失败: ${logtoException}")
                continuation.resume(null)
            } else {
                // 从claims中提取原始的ID token（如果可用）
                continuation.resume(idTokenClaims?.sub) // 使用sub作为标识
            }
        }
    }

    /**
     * 获取刷新令牌
     */
    suspend fun getRefreshToken(): String? = suspendCancellableCoroutine { continuation ->
        // Logto SDK不直接暴露refresh token，这里返回null
        // 实际的token刷新由SDK内部处理
        Log.i(TAG, "刷新令牌由SDK内部管理")
        continuation.resume(null)
    }
    
    /**
     * 执行登录
     */
    suspend fun signIn(activity: Activity): Unit = suspendCancellableCoroutine { continuation ->
        Log.i(TAG, "🔄 开始登录流程")
        Log.d(TAG, "使用的Redirect URI: ${com.cabycare.android.data.auth.LogtoConfig.REDIRECT_URI}")

        try {
            logtoClient?.signIn(activity, com.cabycare.android.data.auth.LogtoConfig.REDIRECT_URI) { logtoException ->
                if (logtoException != null) {
                    Log.e(TAG, "登录失败: ${logtoException}")
                    Log.e(TAG, "错误详情: ${logtoException.message}")
                    Log.e(TAG, "错误类型: ${logtoException.javaClass.simpleName}")

                    // 尝试不同的错误处理策略
                    when {
                        logtoException.message?.contains("INVALID_CALLBACK_URI") == true -> {
                            Log.w(TAG, "⚠️ 回调URI验证失败，尝试备用方案")
                            // 这里可以尝试其他登录方式或显示更友好的错误信息
                        }
                    }

                    continuation.resumeWithException(Exception(logtoException.toString()))
                } else {
                    Log.i(TAG, "✅ 登录成功")
                    continuation.resume(Unit)
                }
            } ?: run {
                Log.e(TAG, "❌ Logto客户端未初始化")
                continuation.resumeWithException(Exception("Logto客户端未初始化"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登录过程中发生异常: ${e.message}")
            continuation.resumeWithException(e)
        }
    }
    
    /**
     * 执行登出
     */
    suspend fun signOut(): Unit = suspendCancellableCoroutine { continuation ->
        logtoClient?.signOut { logtoException ->
            if (logtoException != null) {
                Log.e(TAG, "登出失败: ${logtoException}")
                continuation.resumeWithException(Exception(logtoException.toString()))
            } else {
                Log.i(TAG, "✅ 登出成功")
                continuation.resume(Unit)
            }
        }
    }
    


    /**
     * 使用授权码获取令牌（首次登录时使用）
     */
    suspend fun getTokenWithAuthCode(
        authCode: String,
        codeVerifier: String? = null
    ): LogtoTokenResponse? = withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "🔑 使用授权码获取令牌")

            val response = logtoTokenService.getTokenWithAuthCode(
                code = authCode,
                redirectUri = com.cabycare.android.data.auth.LogtoConfig.REDIRECT_URI,
                clientId = com.cabycare.android.data.auth.LogtoConfig.CLIENT_ID,
                codeVerifier = codeVerifier
            )

            Log.i(TAG, "✅ 授权码令牌获取成功")
            Log.d(TAG, "访问令牌前缀: ${response.accessToken.take(20)}...")
            Log.d(TAG, "刷新令牌: ${if (response.refreshToken != null) "已获取" else "未获取"}")

            return@withContext response

        } catch (e: Exception) {
            Log.e(TAG, "❌ 授权码令牌获取失败: ${e.message}", e)
            return@withContext null
        }
    }
    
    /**
     * 获取用户信息
     */
    suspend fun getUserInfo(): Map<String, Any>? = suspendCancellableCoroutine { continuation ->
        logtoClient?.fetchUserInfo { logtoException, userInfo ->
            if (logtoException != null) {
                Log.e(TAG, "获取用户信息失败: ${logtoException}")
                continuation.resume(null)
            } else {
                Log.i(TAG, "✅ 获取用户信息成功")
                // 将UserInfoResponse转换为Map
                val userMap = userInfo?.let { info ->
                    mapOf(
                        "sub" to (info.sub ?: ""),
                        "email" to (info.email ?: ""),
                        "name" to (info.name ?: "")
                    )
                }
                continuation.resume(userMap)
            }
        }
    }


}
