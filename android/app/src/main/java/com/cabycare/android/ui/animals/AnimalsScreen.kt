package com.cabycare.android.ui.animals

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import com.cabycare.android.data.model.CatProfile
import com.cabycare.android.ui.components.CircularCatAvatarView
import com.cabycare.android.data.local.UserPreferences
import okhttp3.OkHttpClient
import javax.inject.Inject
import androidx.compose.ui.graphics.Color

/**
 * 宠物管理页面
 * 显示宠物列表和管理功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnimalsScreen(
    viewModel: AnimalsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadCats()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部标题和添加按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "我的宠物",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            FloatingActionButton(
                onClick = { viewModel.showAddCatDialog() },
                modifier = Modifier.size(56.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加宠物"
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.error != null -> {
                ErrorCard(
                    error = uiState.error ?: "Unknown error",
                    onRetry = { viewModel.loadCats() }
                )
            }
            
            uiState.cats.isEmpty() -> {
                EmptyStateCard(
                    onAddCat = { viewModel.showAddCatDialog() }
                )
            }
            
            else -> {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(uiState.cats) { cat ->
                        CatCard(
                            cat = cat,
                            onEditClick = { viewModel.editCat(cat) },
                            onDeleteClick = { viewModel.deleteCat(cat.id) },
                            onCardClick = { viewModel.showCatDetail(cat) },
                            userPreferences = viewModel.userPreferences,
                            okHttpClient = viewModel.okHttpClient
                        )
                    }
                }
            }
        }
    }
    
    // 添加/编辑宠物对话框
    if (uiState.showAddEditDialog) {
        AddEditCatDialog(
            cat = uiState.editingCat,
            onDismiss = { viewModel.hideAddCatDialog() },
            onSave = { cat -> viewModel.saveCat(cat) }
        )
    }
    
    // 宠物详情对话框
    uiState.selectedCat?.let { selectedCat ->
        if (uiState.showDetailDialog) {
            CatDetailDialog(
                cat = selectedCat,
                onDismiss = { viewModel.hideCatDetail() },
                userPreferences = viewModel.userPreferences,
                okHttpClient = viewModel.okHttpClient
            )
        }
    }
}

/**
 * 宠物卡片
 */
@Composable
fun CatCard(
    cat: CatProfile,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onCardClick: () -> Unit,
    userPreferences: UserPreferences,
    okHttpClient: OkHttpClient
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        onClick = onCardClick
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 宠物头像 - 使用新的带认证的CatAvatarView
                CircularCatAvatarView(
                    avatarUrl = cat.avatarUrl,
                    size = 64.dp,
                    userPreferences = userPreferences,
                    okHttpClient = okHttpClient
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 宠物信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = cat.name,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "${cat.formattedAge} • ${cat.genderEnum.displayName}",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = cat.typeEnum.displayName,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                // 健康状态徽章
                HealthStatusBadge(status = cat.healthStatus)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 宠物详细信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                InfoItem(
                    icon = Icons.Default.MonitorWeight,
                    label = "体重",
                    value = cat.weight
                )
                InfoItem(
                    icon = Icons.Default.TrendingUp,
                    label = "活跃度",
                    value = cat.activityLevelEnum.displayName
                )
                InfoItem(
                    icon = Icons.Default.Favorite,
                    label = "健康",
                    value = cat.healthStatusEnum.displayName
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                TextButton(onClick = onEditClick) {
                    Text("编辑")
                }
                TextButton(
                    onClick = onDeleteClick,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("删除")
                }
            }
        }
    }
}

/**
 * 信息项组件
 */
@Composable
fun InfoItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
        Text(
            text = label,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}

/**
 * 健康状态徽章
 */
@Composable
fun HealthStatusBadge(status: String) {
    val backgroundColor = when (status.lowercase()) {
        "健康", "正常" -> MaterialTheme.colorScheme.primary
        "注意", "观察" -> Color(0xFFFF9800) // Orange
        "异常", "生病" -> MaterialTheme.colorScheme.error
        else -> MaterialTheme.colorScheme.outline
    }
    
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = backgroundColor,
        modifier = Modifier.padding(4.dp)
    ) {
        Text(
            text = status,
            color = Color.White,
            fontSize = 10.sp,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

/**
 * 错误卡片
 */
@Composable
fun ErrorCard(
    error: String,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "错误",
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "加载失败",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Text(
                text = error,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("重试")
            }
        }
    }
}

/**
 * 空状态卡片
 */
@Composable
fun EmptyStateCard(
    onAddCat: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Pets,
                contentDescription = "暂无宠物",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "还没有添加宠物",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = "点击下方按钮添加您的第一只宠物",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(24.dp))
            Button(
                onClick = onAddCat,
                modifier = Modifier.padding(horizontal = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("添加宠物")
            }
        }
    }
}
