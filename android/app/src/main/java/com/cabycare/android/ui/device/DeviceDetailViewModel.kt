package com.cabycare.android.ui.device

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.NetworkResult
import com.cabycare.android.data.repository.DeviceSettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 设备详情UI状态
 */
data class DeviceDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val deviceStatus: DeviceStatusResponse? = null,
    val otaSettings: DeviceOTASettings? = null,
    val otaStatus: DeviceOTAStatusResponse? = null,
    val sensorStatus: DeviceSensorStatusResponse? = null,
    val isRefreshingStatus: Boolean = false,
    val isRefreshingSensorStatus: Boolean = false,
    val isLoadingOTA: Boolean = false,
    val isSaving: Boolean = false,
    val showSuccess: Boolean = false,
    val showOTASuccess: Boolean = false
)

/**
 * 设备详情ViewModel
 */
@HiltViewModel
class DeviceDetailViewModel @Inject constructor(
    private val deviceSettingsRepository: DeviceSettingsRepository
) : ViewModel() {

    companion object {
        private const val TAG = "DeviceDetailViewModel"
    }

    private val _uiState = MutableStateFlow(DeviceDetailUiState())
    val uiState: StateFlow<DeviceDetailUiState> = _uiState.asStateFlow()

    /**
     * 加载设备详情数据
     */
    fun loadDeviceDetail(deviceId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // 并行加载所有数据
                val statusResult = deviceSettingsRepository.getDeviceStatus(deviceId)
                val otaSettingsResult = deviceSettingsRepository.getDeviceOTASettings(deviceId)
                val otaStatusResult = deviceSettingsRepository.getDeviceOTAStatus(deviceId)
                val sensorStatusResult = deviceSettingsRepository.getDeviceSensorStatus(deviceId)

                // 处理设备状态
                val deviceStatus = when (statusResult) {
                    is NetworkResult.Success -> statusResult.data
                    is NetworkResult.Error -> {
                        Log.e(TAG, "获取设备状态失败", statusResult.exception)
                        null
                    }
                    is NetworkResult.Loading -> null
                }

                // 处理OTA设置
                val otaSettings = when (otaSettingsResult) {
                    is NetworkResult.Success -> otaSettingsResult.data
                    is NetworkResult.Error -> {
                        Log.e(TAG, "获取OTA设置失败", otaSettingsResult.exception)
                        null
                    }
                    is NetworkResult.Loading -> null
                }

                // 处理OTA状态
                val otaStatus = when (otaStatusResult) {
                    is NetworkResult.Success -> otaStatusResult.data
                    is NetworkResult.Error -> {
                        Log.e(TAG, "获取OTA状态失败", otaStatusResult.exception)
                        null
                    }
                    is NetworkResult.Loading -> null
                }

                // 处理传感器状态
                val sensorStatus = when (sensorStatusResult) {
                    is NetworkResult.Success -> sensorStatusResult.data
                    is NetworkResult.Error -> {
                        Log.e(TAG, "获取传感器状态失败", sensorStatusResult.exception)
                        null
                    }
                    is NetworkResult.Loading -> null
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    deviceStatus = deviceStatus,
                    otaSettings = otaSettings,
                    otaStatus = otaStatus,
                    sensorStatus = sensorStatus,
                    error = if (deviceStatus == null) "加载设备信息失败" else null
                )

            } catch (e: Exception) {
                Log.e(TAG, "加载设备详情失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载设备详情失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 刷新设备状态
     */
    fun refreshDeviceStatus(deviceId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshingStatus = true)

            when (val result = deviceSettingsRepository.getDeviceStatus(deviceId)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isRefreshingStatus = false,
                        deviceStatus = result.data
                    )
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "刷新设备状态失败", result.exception)
                    _uiState.value = _uiState.value.copy(
                        isRefreshingStatus = false,
                        error = "刷新设备状态失败: ${result.exception.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    // Loading状态已经在上面设置了
                }
            }
        }
    }

    /**
     * 刷新传感器状态
     */
    fun refreshSensorStatus(deviceId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshingSensorStatus = true)

            when (val result = deviceSettingsRepository.getDeviceSensorStatus(deviceId)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isRefreshingSensorStatus = false,
                        sensorStatus = result.data
                    )
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "刷新传感器状态失败", result.exception)
                    _uiState.value = _uiState.value.copy(
                        isRefreshingSensorStatus = false,
                        error = "刷新传感器状态失败: ${result.exception.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    // Loading状态已经在上面设置了
                }
            }
        }
    }

    /**
     * 更新设备基本信息
     */
    fun updateDeviceBasicInfo(deviceId: String, name: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true, error = null)

            when (val result = deviceSettingsRepository.updateDeviceBasicInfo(deviceId, name)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        deviceStatus = result.data,
                        showSuccess = true
                    )
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "更新设备信息失败", result.exception)
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        error = "保存失败: ${result.exception.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    // Loading状态已经在上面设置了
                }
            }
        }
    }

    /**
     * 更新OTA设置
     */
    fun updateOTASettings(
        deviceId: String,
        autoOtaEnabled: Boolean,
        idleUpdateStartHour: Int? = null,
        idleUpdateEndHour: Int? = null
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true, error = null)

            when (val result = deviceSettingsRepository.updateDeviceOTASettings(
                deviceId, autoOtaEnabled, idleUpdateStartHour, idleUpdateEndHour
            )) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        otaSettings = result.data,
                        showOTASuccess = true
                    )
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "更新OTA设置失败", result.exception)
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        error = "OTA设置保存失败: ${result.exception.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    // Loading状态已经在上面设置了
                }
            }
        }
    }

    /**
     * 重新加载OTA设置
     */
    fun reloadOTASettings(deviceId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoadingOTA = true)

            when (val result = deviceSettingsRepository.getDeviceOTASettings(deviceId)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoadingOTA = false,
                        otaSettings = result.data
                    )
                }
                is NetworkResult.Error -> {
                    Log.e(TAG, "重新加载OTA设置失败", result.exception)
                    _uiState.value = _uiState.value.copy(
                        isLoadingOTA = false,
                        error = "加载OTA设置失败: ${result.exception.message}"
                    )
                }
                is NetworkResult.Loading -> {
                    // Loading状态已经在上面设置了
                }
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 清除成功状态
     */
    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(showSuccess = false, showOTASuccess = false)
    }
}
