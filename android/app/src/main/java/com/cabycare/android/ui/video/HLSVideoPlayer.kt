package com.cabycare.android.ui.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.VideoCall
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.ui.PlayerView
import com.cabycare.android.data.model.VideoSegment
import com.cabycare.android.data.local.UserPreferences
import android.util.Log
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import androidx.media3.exoplayer.hls.HlsMediaSource
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.LoadControl
import androidx.media3.exoplayer.upstream.DefaultAllocator
import androidx.media3.exoplayer.upstream.DefaultLoadErrorHandlingPolicy
import androidx.media3.common.C
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import com.cabycare.android.utils.AttributionUtils
import androidx.media3.exoplayer.DefaultRenderersFactory

/**
 * HLS视频播放器组件
 * 支持HLS流媒体播放，使用ExoPlayer
 */
@Composable
fun HLSVideoPlayer(
    videoSegment: VideoSegment?,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onVideoError: (String) -> Unit,
    modifier: Modifier = Modifier,
    userPreferences: UserPreferences // 修改：使用UserPreferences实时获取token
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            if (videoSegment != null) {
                // 视频播放区域
                ExoPlayerVideoView(
                    videoSegment = videoSegment,
                    isPlaying = isPlaying,
                    onPlayClick = onPlayClick,
                    onVideoError = onVideoError,
                    userPreferences = userPreferences // 传递UserPreferences
                )
            } else {
                // 无视频状态
                NoVideoContent()
            }
        }
    }
}

/**
 * ExoPlayer视频播放组件
 */
@Composable
private fun ExoPlayerVideoView(
    videoSegment: VideoSegment,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    onVideoError: (String) -> Unit,
    userPreferences: UserPreferences // 修改：使用UserPreferences实时获取token
) {
    val context = LocalContext.current
    var player by remember { mutableStateOf<ExoPlayer?>(null) }
    var showControls by remember { mutableStateOf(true) }
    var isBuffering by remember { mutableStateOf(false) }
    var bufferPercentage by remember { mutableIntStateOf(0) }
    var bufferedPosition by remember { mutableLongStateOf(0L) }
    
    // 🎯 新增：缓冲监控状态
    var currentPosition by remember { mutableLongStateOf(0L) }
    var totalDuration by remember { mutableLongStateOf(0L) }
    
    // 🚀 定期监控缓冲状态
    LaunchedEffect(player) {
        player?.let { exoPlayer ->
            while (true) {
                try {
                    val buffered = exoPlayer.bufferedPercentage
                    val bufferedPos = exoPlayer.bufferedPosition
                    val currentPos = exoPlayer.currentPosition
                    val duration = exoPlayer.duration
                    
                    // 更新状态
                    bufferPercentage = buffered
                    bufferedPosition = bufferedPos
                    currentPosition = currentPos
                    totalDuration = duration
                    
                    // 详细的缓冲日志
                    if (buffered > 0) {
                        val bufferedSeconds = bufferedPos / 1000
                        val currentSeconds = currentPos / 1000
                        val bufferAhead = (bufferedPos - currentPos) / 1000
                        
                        Log.d("HLSVideoPlayer", "📊 缓冲状态: ${buffered}% | 当前: ${currentSeconds}s | 已缓冲: ${bufferedSeconds}s | 预缓冲: ${bufferAhead}s")
                        
                        // 智能缓冲管理（提高阈值，适应新的激进缓冲策略）
                        if (bufferAhead < 8 && exoPlayer.isPlaying) {
                            Log.w("HLSVideoPlayer", "⚠️ 缓冲严重不足: 仅剩${bufferAhead}秒缓冲，暂停播放等待缓冲")
                            // 自动暂停播放，等待更多缓冲
                            exoPlayer.pause()
                        } else if (bufferAhead < 15 && exoPlayer.isPlaying) {
                            Log.w("HLSVideoPlayer", "⚠️ 缓冲不足警告: 仅剩${bufferAhead}秒缓冲")
                        } else if (bufferAhead > 20) {
                            Log.d("HLSVideoPlayer", "✅ 缓冲充足: ${bufferAhead}秒预缓冲")
                            // 如果之前因为缓冲不足暂停了，现在可以恢复播放
                            if (!exoPlayer.isPlaying && isPlaying) {
                                Log.d("HLSVideoPlayer", "缓冲充足，恢复播放")
                                exoPlayer.play()
                            }
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.e("HLSVideoPlayer", "缓冲监控异常: ${e.message}")
                }
                
                kotlinx.coroutines.delay(2000) // 每2秒检查一次
            }
        }
    }
    
    /**
     * 获取当前有效的访问令牌
     */
    fun getCurrentAccessToken(): String? {
        return runCatching {
            runBlocking {
                // 首先尝试从完整用户信息获取最新token
                val tokenFromCompleteInfo = userPreferences.getAccessTokenFromCompleteInfo().first()
                if (!tokenFromCompleteInfo.isNullOrEmpty()) {
                    Log.d("HLSVideoPlayer", "✅ 使用完整用户信息中的token: ${tokenFromCompleteInfo.take(20)}...")
                    return@runBlocking tokenFromCompleteInfo
                }

                // 向后兼容：从旧存储方式获取
                val tokenFromOldStorage = userPreferences.getAccessToken().first()
                if (!tokenFromOldStorage.isNullOrEmpty()) {
                    Log.d("HLSVideoPlayer", "✅ 使用旧存储方式的token: ${tokenFromOldStorage.take(20)}...")
                    return@runBlocking tokenFromOldStorage
                }

                Log.w("HLSVideoPlayer", "⚠️ 未找到有效的访问令牌")
                return@runBlocking null
            }
        }.getOrNull()
    }

    // 创建HTTP数据源工厂（记忆变量，用于在多个LaunchedEffect间共享）
    val httpDataSourceFactory = remember {
        DefaultHttpDataSource.Factory().apply {
            // 设置用户代理
            setUserAgent("CabyCare-Android/1.0")

            // 添加认证头（实时获取最新token）
            val currentToken = getCurrentAccessToken()
            if (currentToken != null) {
                setDefaultRequestProperties(mapOf(
                    "Authorization" to "Bearer $currentToken"
                ))
                Log.d("HLSVideoPlayer", "已设置认证头用于HLS播放")
            } else {
                Log.w("HLSVideoPlayer", "警告：没有提供访问令牌，可能无法播放需要认证的视频")
            }

            // 🚀 优化网络连接和缓冲设置
            setConnectTimeoutMs(15000) // 15秒连接超时（增加超时时间）
            setReadTimeoutMs(30000)    // 30秒读取超时（增加读取超时）

            // 允许跨协议重定向
            setAllowCrossProtocolRedirects(true)

            // 🎯 设置保持连接活跃
            setKeepPostFor302Redirects(true)
        }
    }
    
    // 初始化ExoPlayer
    LaunchedEffect(Unit) {
        try {
            // 🎯 使用 attribution context 进行媒体播放
            val attributionContext = AttributionUtils.getMediaPlaybackContext(context)
            
            // 🔧 创建优化的渲染器工厂来防止崩溃
            val renderersFactory = DefaultRenderersFactory(attributionContext).apply {
                // 使用平台渲染器优先，避免扩展渲染器导致的崩溃
                setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_OFF)
                // 启用解码器回退机制
                setEnableDecoderFallback(true)
                // 禁用音频浮点输出以提高兼容性
                setEnableAudioFloatOutput(false)
                // 启用异步处理以减少主线程阻塞
                setEnableAudioTrackPlaybackParams(true)
            }
            
            // 创建媒体源工厂
            val mediaSourceFactory = DefaultMediaSourceFactory(httpDataSourceFactory)
            
            // 🚀 创建自定义 LoadControl 来优化缓冲策略
            val loadControl = DefaultLoadControl.Builder()
                .setAllocator(DefaultAllocator(true, 64))  // 使用64KB块大小的分配器（更大的缓冲块）
                .setBufferDurationsMs(
                    30000,  // minBufferMs - 最小缓冲30秒（更激进的预缓冲）
                    120000, // maxBufferMs - 最大缓冲120秒（增加最大缓冲）  
                    1000,   // bufferForPlaybackMs - 开始播放仅需1秒缓冲（更快开始）
                    2000    // bufferForPlaybackAfterRebufferMs - 重新缓冲后需要2秒才开始播放（更快恢复）
                )
                .setTargetBufferBytes(50 * 1024 * 1024)  // 设置50MB目标缓冲字节数
                .setPrioritizeTimeOverSizeThresholds(true)  // 优先考虑时间而不是大小
                .setBackBuffer(30000, true)  // 保留30秒的后台缓冲
                .build()
            
            // 初始化ExoPlayer并配置自定义LoadControl
            player = ExoPlayer.Builder(attributionContext)
                .setRenderersFactory(renderersFactory)  // 使用自定义渲染器工厂
                .setMediaSourceFactory(mediaSourceFactory)
                .setLoadControl(loadControl)  // 应用自定义缓冲控制
                .build().apply {
                    addListener(object : Player.Listener {
                        override fun onPlayerError(error: PlaybackException) {
                            // 过滤掉系统级错误，防止不必要的错误报告
                            val isSystemResourceError = error.message?.contains("Failed to query component interface") == true ||
                                                       error.message?.contains("BAD_INDEX") == true ||
                                                       error.message?.contains("Renderer process") == true ||
                                                       error.message?.contains("MediaCodec") == true
                            
                            if (isSystemResourceError) {
                                Log.d("HLSVideoPlayer", "系统级 MediaCodec 查询问题，通常不影响播放: ${error.message}")
                            } else {
                                Log.e("HLSVideoPlayer", "播放错误: ${error.message}")
                                Log.e("HLSVideoPlayer", "错误详情: ${error.cause}")
                                Log.e("HLSVideoPlayer", "错误代码: ${error.errorCode}")
                                onVideoError("视频播放失败: ${error.message}")
                            }
                        }
                        
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            when (playbackState) {
                                Player.STATE_READY -> {
                                    isBuffering = false
                                    bufferPercentage = bufferedPercentage
                                    bufferedPosition = bufferedPosition
                                    Log.d("HLSVideoPlayer", "视频准备就绪，缓冲充足 (${bufferPercentage}%)")
                                    showControls = false
                                }
                                Player.STATE_BUFFERING -> {
                                    isBuffering = true
                                    bufferPercentage = bufferedPercentage
                                    bufferedPosition = bufferedPosition
                                    Log.d("HLSVideoPlayer", "视频缓冲中... 缓冲百分比: ${bufferPercentage}%")
                                }
                                Player.STATE_ENDED -> {
                                    isBuffering = false
                                    Log.d("HLSVideoPlayer", "视频播放结束")
                                    showControls = true
                                }
                                Player.STATE_IDLE -> {
                                    isBuffering = false
                                    Log.d("HLSVideoPlayer", "播放器空闲状态")
                                }
                            }
                        }
                        
                        override fun onIsLoadingChanged(isLoading: Boolean) {
                            if (isLoading) {
                                bufferPercentage = bufferedPercentage
                                bufferedPosition = bufferedPosition
                            }
                            Log.d("HLSVideoPlayer", "加载状态变化: $isLoading, 缓冲位置: ${bufferedPosition}ms, 缓冲百分比: ${bufferPercentage}%")
                        }
                        
                        override fun onMediaItemTransition(mediaItem: androidx.media3.common.MediaItem?, reason: Int) {
                            Log.d("HLSVideoPlayer", "媒体项切换: ${mediaItem?.localConfiguration?.uri}")
                        }
                    })
                }
                
            Log.d("HLSVideoPlayer", "ExoPlayer初始化成功，已配置优化缓冲策略")
            
        } catch (e: Exception) {
            Log.e("HLSVideoPlayer", "初始化播放器失败", e)
            onVideoError("播放器初始化失败: ${e.message}")
        }
    }
    
    // 更新视频URL和播放状态
    LaunchedEffect(videoSegment.fileUrl, isPlaying) {
        player?.let { exoPlayer ->
            try {
                Log.d("HLSVideoPlayer", "准备加载HLS视频: ${videoSegment.fileUrl}")
                
                // 明确指定为 HLS 内容
                val mediaItem = MediaItem.Builder()
                    .setUri(videoSegment.fileUrl)
                    .setMimeType(MimeTypes.APPLICATION_M3U8)
                    .build()
                
                // 🚀 优化 HLS MediaSource 配置，添加更激进的缓冲策略
                val hlsMediaSource = HlsMediaSource.Factory(httpDataSourceFactory)
                    .setAllowChunklessPreparation(true)  // 允许无chunk准备，更快开始播放
                    .setLoadErrorHandlingPolicy(DefaultLoadErrorHandlingPolicy(3)) // 设置加载错误处理策略
                    .createMediaSource(mediaItem)
                
                // 设置媒体源
                exoPlayer.setMediaSource(hlsMediaSource)
                exoPlayer.prepare()
                
                // 🎯 设置播放参数来优化缓冲体验
                exoPlayer.setWakeMode(C.WAKE_MODE_NETWORK)  // 保持网络唤醒
                
                // 根据isPlaying状态控制播放，但确保有足够缓冲
                if (isPlaying) {
                    // 检查缓冲状态，如果缓冲不足，先等待缓冲
                    val bufferAhead = (exoPlayer.bufferedPosition - exoPlayer.currentPosition) / 1000
                    if (bufferAhead < 5) {
                        Log.d("HLSVideoPlayer", "缓冲不足(${bufferAhead}秒)，等待更多缓冲后再开始播放")
                        // 先暂停，等待缓冲
                        exoPlayer.pause()
                        // 设置一个延迟检查，等缓冲足够后再播放
                        kotlinx.coroutines.delay(2000)
                        val newBufferAhead = (exoPlayer.bufferedPosition - exoPlayer.currentPosition) / 1000
                        if (newBufferAhead >= 5) {
                            exoPlayer.play()
                            Log.d("HLSVideoPlayer", "缓冲充足(${newBufferAhead}秒)，开始播放HLS视频")
                        }
                    } else {
                        exoPlayer.play()
                        Log.d("HLSVideoPlayer", "开始播放HLS视频，当前缓冲: ${exoPlayer.bufferedPercentage}%，预缓冲: ${bufferAhead}秒")
                    }
                } else {
                    exoPlayer.pause()
                    Log.d("HLSVideoPlayer", "暂停播放HLS视频")
                }
                
            } catch (e: Exception) {
                Log.e("HLSVideoPlayer", "设置HLS媒体项失败", e)
                onVideoError("加载HLS视频失败: ${e.message}")
            }
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            player?.release()
            player = null
            Log.d("HLSVideoPlayer", "释放播放器资源")
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // ExoPlayer视图 - 使用更安全的配置
        AndroidView(
            factory = { ctx ->
                PlayerView(ctx).apply {
                    this.player = player
                    useController = false // 禁用默认控制器，使用自定义控制器
                    setBackgroundColor(android.graphics.Color.BLACK)
                    // 设置更安全的视频渲染配置
                    setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
                    // 禁用手势控制以避免冲突
                    setUseArtwork(false)
                    setDefaultArtwork(null)
                    // 设置错误消息显示
                    setErrorMessageProvider { error ->
                        android.util.Pair(0, "视频播放出现问题，请稍后重试")
                    }
                }
            },
            update = { playerView ->
                try {
                    playerView.player = player
                } catch (e: Exception) {
                    Log.w("HLSVideoPlayer", "更新PlayerView时出现异常: ${e.message}")
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 缓冲状态指示器
        if (isBuffering) {
            BufferingOverlay(
                bufferPercentage = bufferPercentage,
                modifier = Modifier.align(Alignment.Center)
            )
        }
        
        // 自定义播放控制器（在视频上方显示）
        if (showControls) {
            VideoControlOverlay(
                videoSegment = videoSegment,
                isPlaying = isPlaying,
                onPlayClick = onPlayClick,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 视频信息覆盖层（包含缓冲信息）
        VideoInfoOverlay(
            videoSegment = videoSegment,
            bufferPercentage = bufferPercentage,
            bufferedPosition = bufferedPosition,
            currentPosition = currentPosition,
            totalDuration = totalDuration,
            isBuffering = isBuffering,
            modifier = Modifier.align(Alignment.BottomStart)
        )
        
        // 点击区域来显示/隐藏控制器
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable {
                    showControls = !showControls
                }
        )
    }
}

/**
 * 视频控制覆盖层
 */
@Composable
private fun VideoControlOverlay(
    videoSegment: VideoSegment,
    isPlaying: Boolean,
    onPlayClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.3f)),
        contentAlignment = Alignment.Center
    ) {
        // 播放/暂停按钮
        IconButton(
            onClick = onPlayClick,
            modifier = Modifier.size(64.dp)
        ) {
            Icon(
                imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                contentDescription = if (isPlaying) "暂停" else "播放",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

/**
 * 视频信息覆盖层
 */
@Composable
private fun VideoInfoOverlay(
    videoSegment: VideoSegment,
    bufferPercentage: Int = 0,
    bufferedPosition: Long = 0L,
    currentPosition: Long = 0L,
    totalDuration: Long = 0L,
    isBuffering: Boolean = false,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .padding(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(6.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = "设备: ${videoSegment.deviceId}",
                color = Color.White,
                fontSize = 12.sp
            )
            Text(
                text = "时长: ${videoSegment.formattedDuration}",
                color = Color.White,
                fontSize = 12.sp
            )
            
            // 播放进度信息
            if (currentPosition > 0 || totalDuration > 0) {
                val currentSec = currentPosition / 1000
                val totalSec = totalDuration / 1000
                val progressPercent = if (totalSec > 0) (currentSec * 100 / totalSec).toInt() else 0
                
                Text(
                    text = "进度: ${currentSec}s/${totalSec}s (${progressPercent}%)",
                    color = Color.Cyan.copy(alpha = 0.9f),
                    fontSize = 10.sp
                )
            }
            
            // 缓冲信息
            if (bufferPercentage > 0 || bufferedPosition > 0) {
                val bufferAhead = (bufferedPosition - currentPosition) / 1000
                val statusColor = when {
                    isBuffering -> Color.Yellow
                    bufferAhead < 5 -> Color.Red
                    bufferAhead < 15 -> Color(0xFFFF9800) // Orange color
                    else -> Color.Green
                }
                
                Text(
                    text = "缓冲: ${bufferPercentage}% (+${bufferAhead}s)",
                    color = statusColor.copy(alpha = 0.8f),
                    fontSize = 10.sp
                )
            }
            
            // 缓冲状态指示
            if (isBuffering) {
                Text(
                    text = "🔄 正在缓冲...",
                    color = Color.Yellow,
                    fontSize = 10.sp
                )
            }
            
            // 显示播放URL的一部分（用于调试）
            Text(
                text = "URL: ${videoSegment.fileUrl.takeLast(30)}",
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 10.sp
            )
        }
    }
}

/**
 * 无视频内容显示
 */
@Composable
private fun NoVideoContent() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.VideoCall,
            contentDescription = "暂无视频",
            tint = Color.White.copy(alpha = 0.6f),
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "请选择视频播放",
            color = Color.White.copy(alpha = 0.8f),
            fontSize = 14.sp
        )
    }
}

/**
 * 缓冲状态覆盖层
 */
@Composable
private fun BufferingOverlay(
    bufferPercentage: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(20.dp)
        ) {
            CircularProgressIndicator(
                color = Color.White,
                strokeWidth = 3.dp,
                modifier = Modifier.size(40.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "缓冲中...",
                color = Color.White,
                fontSize = 14.sp
            )
            
            if (bufferPercentage > 0) {
                Text(
                    text = "$bufferPercentage%",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 12.sp
                )
            }
        }
    }
} 