package com.cabycare.android.di

import android.content.Context
import com.cabycare.android.data.local.UserPreferences
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkManager
import com.cabycare.android.data.api.DeviceSettingsApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * 网络模块
 * 提供网络相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    /**
     * 提供基础OkHttpClient实例（用于其他可能需要的组件）
     * 注意：这个OkHttpClient会使用NetworkManager的认证拦截器
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(networkManager: NetworkManager): OkHttpClient {
        // 直接使用NetworkManager创建的OkHttpClient，确保包含认证拦截器
        return networkManager.createRetrofit().callFactory() as OkHttpClient
    }

    /**
     * 提供NetworkManager实例
     */
    @Provides
    @Singleton
    fun provideNetworkManager(userPreferences: UserPreferences): NetworkManager {
        return NetworkManager(userPreferences)
    }

    /**
     * 提供ApiService实例
     * 直接使用NetworkManager创建，避免循环依赖
     */
    @Provides
    @Singleton
    fun provideApiService(networkManager: NetworkManager): ApiService {
        return networkManager.createApiService<ApiService>()
    }

    /**
     * 提供DeviceSettingsApiService实例
     */
    @Provides
    @Singleton
    fun provideDeviceSettingsApiService(networkManager: NetworkManager): DeviceSettingsApiService {
        return networkManager.createApiService<DeviceSettingsApiService>()
    }

    /**
     * 提供CatApiService实例
     */
    @Provides
    @Singleton
    fun provideCatApiService(networkManager: NetworkManager): com.cabycare.android.data.api.CatApiService {
        return networkManager.createApiService<com.cabycare.android.data.api.CatApiService>()
    }

    /**
     * 提供BluetoothManager实例
     */
    @Provides
    @Singleton
    fun provideBluetoothManager(
        @ApplicationContext context: Context
    ): com.cabycare.android.bluetooth.BluetoothManager {
        return com.cabycare.android.bluetooth.BluetoothManager(context)
    }
}
