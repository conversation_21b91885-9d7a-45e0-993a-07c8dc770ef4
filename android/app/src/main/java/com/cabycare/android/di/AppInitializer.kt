package com.cabycare.android.di

import android.util.Log
import com.cabycare.android.data.auth.AuthManager
import com.cabycare.android.data.network.NetworkManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用初始化器
 * 负责在应用启动时进行必要的初始化工作
 */
@Singleton
class AppInitializer @Inject constructor(
    private val authManager: AuthManager,
    private val networkManager: NetworkManager
) {
    companion object {
        private const val TAG = "AppInitializer"
    }
    
    /**
     * 初始化应用
     */
    fun initialize() {
        Log.i(TAG, "🚀 开始应用初始化")
        
        // 注册NetworkManager为AuthManager的token更新监听器
        authManager.addTokenUpdateListener(networkManager)
        Log.i(TAG, "✅ 已注册NetworkManager为token更新监听器")
        
        Log.i(TAG, "✅ 应用初始化完成")
    }
}
