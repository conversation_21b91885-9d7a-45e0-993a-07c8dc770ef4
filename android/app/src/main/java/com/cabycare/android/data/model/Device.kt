package com.cabycare.android.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 设备状态枚举
 */
enum class DeviceStatus(val value: Int, val displayName: String) {
    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    MAINTENANCE(2, "维护中"),
    ERROR(3, "故障");
    
    companion object {
        fun fromValue(value: Int): DeviceStatus {
            return values().find { it.value == value } ?: OFFLINE
        }
    }
}

/**
 * 设备模型
 * 对应Swift版本的Device模型
 */
@Serializable
data class Device(
    @SerialName("device_id")
    val deviceId: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("hardware_sn")
    val hardwareSn: String,
    val name: String,
    val model: String,
    val timezone: String,
    @SerialName("firmware_version")
    val firmwareVersion: String,
    val status: Int,
    @SerialName("last_heartbeat")
    val lastHeartbeat: String? = null,
    @SerialName("last_active")
    val lastActive: String? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 设备是否在线
     */
    val isOnline: Boolean
        get() = status == DeviceStatus.ONLINE.value
    
    /**
     * 获取设备状态枚举
     */
    val deviceStatus: DeviceStatus
        get() = DeviceStatus.fromValue(status)
    
    /**
     * 设备显示名称
     */
    val displayName: String
        get() = name.ifEmpty { "设备 $hardwareSn" }
}

/**
 * 可访问设备响应模型
 * 对应Swift版本的AccessibleDevicesResponse模型
 */
@Serializable
data class AccessibleDevicesResponse(
    val code: Int,
    val status: String,
    val message: String,
    val data: List<AccessibleDevice> = emptyList()
) {
    /**
     * 请求是否成功
     */
    val isSuccess: Boolean
        get() = code == 200 || code == 0
}

/**
 * 可访问设备模型
 * 对应Swift版本的AccessibleDevice模型
 */
@Serializable
data class AccessibleDevice(
    @SerialName("device_id")
    val deviceId: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("hardware_sn")
    val hardwareSn: String,
    val name: String,
    val model: String,
    val timezone: String,
    @SerialName("firmware_version")
    val firmwareVersion: String,
    val status: Int,
    @SerialName("last_heartbeat")
    val lastHeartbeat: String? = null,
    @SerialName("last_active")
    val lastActive: String? = null,
    @SerialName("created_at")
    val createdAt: String,
    @SerialName("updated_at")
    val updatedAt: String
) {
    /**
     * 设备是否在线
     */
    val online: Boolean
        get() = status == 1

    /**
     * 设备是否在线（别名，用于UI兼容性）
     */
    val isOnline: Boolean
        get() = online

    /**
     * 设备显示名称
     */
    val displayName: String
        get() = name.ifEmpty { "设备 $hardwareSn" }
    
    /**
     * 转换为Device模型
     */
    fun toDevice(): Device {
        return Device(
            deviceId = deviceId,
            userId = userId,
            hardwareSn = hardwareSn,
            name = name,
            model = model,
            timezone = timezone,
            firmwareVersion = firmwareVersion,
            status = status,
            lastHeartbeat = lastHeartbeat,
            lastActive = lastActive,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}

/**
 * 设备状态响应模型
 * 对应Swift版本的DeviceStatusResponse
 */
@Serializable
data class DeviceStatusResponse(
    @SerialName("device_id") val deviceId: String,
    val name: String,
    val model: String? = null,
    val firmware: String? = null,
    val online: Boolean? = null,
    @SerialName("last_seen") val lastSeen: String? = null,
    @SerialName("firmware_version") val firmwareVersion: String? = null,
    val temperature: Double? = null,
    val humidity: Double? = null,
    @SerialName("battery_level") val batteryLevel: Int? = null
)

/**
 * 设备OTA状态枚举
 */
enum class DeviceOTAStatus(val value: String, val displayName: String, val colorName: String) {
    IDLE("idle", "空闲", "gray"),
    UPDATING("updating", "升级中", "blue"),
    FAILED("failed", "升级失败", "red"),
    COMPLETED("completed", "升级完成", "green");
    
    companion object {
        fun fromValue(value: String): DeviceOTAStatus {
            return values().find { it.value == value } ?: IDLE
        }
    }
    
    val systemIcon: String
        get() = when (this) {
            IDLE -> "circle"
            UPDATING -> "refresh"
            FAILED -> "error"
            COMPLETED -> "check_circle"
        }
}

/**
 * 设备OTA状态响应模型
 */
@Serializable
data class DeviceOTAStatusResponse(
    @SerialName("device_id")
    val deviceId: String,
    val status: String,
    @SerialName("last_updated")
    val lastUpdated: String
) {
    /**
     * 获取OTA状态枚举
     */
    val otaStatus: DeviceOTAStatus
        get() = DeviceOTAStatus.fromValue(status)
    
    /**
     * 格式化的最后更新时间
     */
    fun getFormattedLastUpdated(): String {
        // TODO: 实现时间格式化逻辑
        return lastUpdated
    }
    
    /**
     * 相对时间显示
     */
    fun getRelativeTime(): String {
        // TODO: 实现相对时间计算逻辑
        return "刚刚"
    }
}

/**
 * 设备信息（用于添加设备）
 */
@Serializable
data class DeviceInfo(
    val name: String,
    val model: String,
    @SerialName("hardware_sn")
    val hardwareSn: String,
    val timezone: String
)

/**
 * 带实时状态的可访问设备
 * 结合设备基本信息和实时状态
 */
data class AccessibleDeviceWithStatus(
    val device: AccessibleDevice,
    val realTimeStatus: DeviceStatusResponse?, // 实时状态，可能为null（获取失败时）
    val isOnline: Boolean, // 合并后的在线状态
    val lastHeartbeat: String? // 最后心跳时间
) {
    /**
     * 设备显示名称
     */
    val displayName: String
        get() = device.displayName
    
    /**
     * 设备ID
     */
    val deviceId: String
        get() = device.deviceId
    
    /**
     * 设备型号
     */
    val model: String
        get() = device.model
    
    /**
     * 固件版本
     */
    val firmwareVersion: String
        get() = device.firmwareVersion
    
    /**
     * 设备创建时间
     */
    val createdAt: String
        get() = device.createdAt
    
    /**
     * 设备更新时间
     */
    val updatedAt: String
        get() = device.updatedAt
    
    /**
     * 电池电量（来自实时状态）
     */
    val batteryLevel: Int?
        get() = realTimeStatus?.batteryLevel
    
    /**
     * 温度（来自实时状态）
     */
    val temperature: Double?
        get() = realTimeStatus?.temperature
    
    /**
     * 湿度（来自实时状态）
     */
    val humidity: Double?
        get() = realTimeStatus?.humidity
    
    /**
     * 获取状态源描述
     */
    val statusSource: String
        get() = if (realTimeStatus != null) "实时状态" else "缓存状态"
    
    /**
     * 是否有实时状态数据
     */
    val hasRealTimeData: Boolean
        get() = realTimeStatus != null
}
