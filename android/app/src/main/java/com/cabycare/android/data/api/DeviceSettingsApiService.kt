package com.cabycare.android.data.api

import com.cabycare.android.data.model.*
import retrofit2.http.*

/**
 * 设备设置相关API服务
 */
interface DeviceSettingsApiService {

    /**
     * 获取设备状态
     */
    @GET("api/devices/{deviceId}/status")
    suspend fun getDeviceStatus(
        @Path("deviceId") deviceId: String
    ): DeviceStatusResponse

    /**
     * 更新设备基本信息
     */
    @PUT("api/devices/{deviceId}")
    suspend fun updateDeviceBasicInfo(
        @Path("deviceId") deviceId: String,
        @Query("user_id") userId: String,
        @Body request: DeviceBasicInfoUpdateRequest
    ): DeviceStatusResponse

    /**
     * 获取设备OTA设置
     */
    @GET("api/devices/{deviceId}/setting")
    suspend fun getDeviceOTASettings(
        @Path("deviceId") deviceId: String,
        @Query("user_id") userId: String
    ): DeviceOTASettings

    /**
     * 更新设备OTA设置
     */
    @PUT("api/devices/{deviceId}/setting")
    suspend fun updateDeviceOTASettings(
        @Path("deviceId") deviceId: String,
        @Query("user_id") userId: String,
        @Body request: SimpleOTAUpdateRequest
    ): DeviceOTASettings

    /**
     * 获取设备OTA状态
     */
    @GET("api/devices/{deviceId}/ota-status")
    suspend fun getDeviceOTAStatus(
        @Path("deviceId") deviceId: String
    ): DeviceOTAStatusResponse

    /**
     * 获取设备传感器状态
     */
    @GET("api/devices/{deviceId}/sensor-status")
    suspend fun getDeviceSensorStatus(
        @Path("deviceId") deviceId: String,
        @Query("user_id") userId: String
    ): DeviceSensorStatusResponse
}
