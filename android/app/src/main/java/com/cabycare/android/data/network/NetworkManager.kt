package com.cabycare.android.data.network

import android.util.Log
import com.cabycare.android.data.auth.TokenUpdateListener
import com.cabycare.android.data.local.UserPreferences
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络管理器
 * 负责配置Retrofit、OkHttp和网络拦截器
 */
@Singleton
class NetworkManager @Inject constructor(
    private val userPreferences: UserPreferences
) : TokenUpdateListener {
    companion object {
        private const val TAG = "NetworkManager"
        private const val BASE_URL = "https://api.caby.care" // 替换为实际的API地址
        private const val CONNECT_TIMEOUT = 30L
        private const val READ_TIMEOUT = 30L
        private const val WRITE_TIMEOUT = 30L
    }

    // 缓存当前token，用于快速访问
    @Volatile
    private var cachedAccessToken: String? = null
    
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
    }
    
    /**
     * 认证拦截器
     * 自动在请求头中添加Authorization token，并在401错误时自动刷新token
     */
    private inner class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()

            // 获取最新的访问令牌
            val accessToken = getCurrentAccessToken()

            val newRequest = if (!accessToken.isNullOrEmpty()) {
                Log.d(TAG, "🔐 为请求添加Authorization头: ${originalRequest.url}")
                originalRequest.newBuilder()
                    .addHeader("Authorization", "Bearer $accessToken")
                    .build()
            } else {
                Log.w(TAG, "⚠️ 请求 ${originalRequest.url} 没有添加Authorization头")
                originalRequest
            }

            val response = chain.proceed(newRequest)

            // 如果收到401错误，直接返回，不进行自动重试
            // 让调用方处理认证失败的情况
            if (response.code == 401) {
                Log.w(TAG, "🔒 收到401错误，token可能已过期")
            }

            return response
        }
    }
    
    /**
     * 错误处理拦截器
     * 处理通用的HTTP错误和认证过期
     */
    private inner class ErrorHandlingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val response = chain.proceed(request)
            
            when (response.code) {
                401 -> {
                    Log.w(TAG, "认证失败，令牌可能已过期")
                    // 这里可以触发令牌刷新逻辑
                }
                403 -> {
                    Log.w(TAG, "权限不足")
                }
                404 -> {
                    Log.w(TAG, "请求的资源不存在: ${request.url}")
                }
                500, 502, 503, 504 -> {
                    Log.e(TAG, "服务器错误: ${response.code}")
                }
            }
            
            return response
        }
    }
    
    /**
     * 创建OkHttp客户端
     */
    private fun createOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d(TAG, message)
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(AuthInterceptor())
            .addInterceptor(ErrorHandlingInterceptor())
            .addInterceptor(loggingInterceptor)
            .build()
    }
    
    /**
     * 创建Retrofit实例
     */
    fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(createOkHttpClient())
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType()))
            .build()
    }


    
    /**
     * 创建API服务
     */
    inline fun <reified T> createApiService(): T {
        return createRetrofit().create(T::class.java)
    }

    /**
     * 获取当前访问令牌（优先使用缓存，然后从存储获取）
     */
    private fun getCurrentAccessToken(): String? {
        // 首先检查缓存
        cachedAccessToken?.let { cached ->
            Log.d(TAG, "✅ 使用缓存的token: ${cached.take(20)}...")
            return cached
        }

        // 缓存为空，从存储获取
        return runCatching {
            kotlinx.coroutines.runBlocking {
                // 首先尝试从完整用户信息获取最新token
                val tokenFromCompleteInfo = userPreferences.getAccessTokenFromCompleteInfo().first()
                if (!tokenFromCompleteInfo.isNullOrEmpty()) {
                    Log.d(TAG, "✅ 从完整用户信息获取token: ${tokenFromCompleteInfo.take(20)}...")
                    cachedAccessToken = tokenFromCompleteInfo // 更新缓存
                    return@runBlocking tokenFromCompleteInfo
                }

                // 向后兼容：从旧存储方式获取
                val tokenFromOldStorage = userPreferences.getAccessToken().first()
                if (!tokenFromOldStorage.isNullOrEmpty()) {
                    Log.d(TAG, "✅ 从旧存储获取token: ${tokenFromOldStorage.take(20)}...")
                    cachedAccessToken = tokenFromOldStorage // 更新缓存
                    return@runBlocking tokenFromOldStorage
                }

                Log.w(TAG, "⚠️ 未找到有效的访问令牌")
                return@runBlocking null
            }
        }.getOrNull()
    }

    /**
     * 更新缓存的访问令牌
     * 当AuthManager获取到新token时调用此方法
     */
    fun updateCachedToken(newToken: String) {
        Log.i(TAG, "🔄 更新缓存token: ${newToken.take(20)}...")
        cachedAccessToken = newToken
    }

    /**
     * 清除缓存的访问令牌
     */
    fun clearCachedToken() {
        Log.i(TAG, "🗑️ 清除缓存token")
        cachedAccessToken = null
    }

    // 实现TokenUpdateListener接口
    override fun onTokenUpdated(newToken: String) {
        updateCachedToken(newToken)
    }

    override fun onTokenCleared() {
        clearCachedToken()
    }
}

/**
 * 网络结果封装类
 */
sealed class NetworkResult<out T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error(val exception: Throwable) : NetworkResult<Nothing>()
    data class Loading(val isLoading: Boolean = true) : NetworkResult<Nothing>()
}

/**
 * 网络请求扩展函数
 * 统一处理网络请求的异常和结果
 */
suspend fun <T> safeApiCall(
    apiCall: suspend () -> T
): NetworkResult<T> {
    return try {
        NetworkResult.Success(apiCall())
    } catch (e: Exception) {
        Log.e("NetworkManager", "API调用失败", e)
        NetworkResult.Error(e)
    }
}
