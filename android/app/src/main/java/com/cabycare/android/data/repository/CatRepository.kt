package com.cabycare.android.data.repository

import android.util.Log
import com.cabycare.android.data.model.*
import com.cabycare.android.data.network.ApiService
import com.cabycare.android.data.network.NetworkResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 猫咪数据仓库
 * 负责管理猫咪相关的数据操作
 */
@Singleton
class CatRepository @Inject constructor(
    private val apiService: ApiService,
    private val catApiService: com.cabycare.android.data.api.CatApiService
) : BaseRepository() {

    companion object {
        private const val TAG = "CatRepository"
    }
    
    /**
     * 获取猫咪列表
     */
    suspend fun getCats(): NetworkResult<List<CatProfile>> {
        Log.d(TAG, "获取猫咪列表")
        return handleListResult(
            safeApiCall {
                val apiCats = apiService.getCats()
                apiCats.map { mapApiResponseToCatProfile(it) }
            }
        )
    }
    
    /**
     * 获取猫咪详情
     */
    suspend fun getCatDetail(catId: String): NetworkResult<CatProfile> {
        Log.d(TAG, "获取猫咪详情: $catId")
        return handleSingleResult(
            safeApiCall {
                val apiCat = apiService.getCatDetail(catId)
                mapApiResponseToCatProfile(apiCat)
            }
        )
    }
    
    /**
     * 创建猫咪档案
     */
    suspend fun createCat(cat: CatProfile): NetworkResult<CatProfile> {
        Log.d(TAG, "创建猫咪档案: ${cat.name}")
        return handleSingleResult(
            safeApiCall {
                val request = mapCatProfileToCreateRequest(cat)
                val response = apiService.createCat(request)
                if (response.isSuccess) {
                    mapApiResponseToCatProfile(response.data)
                } else {
                    throw RuntimeException(response.message)
                }
            }
        )
    }

    /**
     * 更新猫咪档案
     */
    suspend fun updateCat(catId: String, cat: CatProfile): NetworkResult<CatProfile> {
        Log.d(TAG, "更新猫咪档案: $catId")
        return handleSingleResult(
            safeApiCall {
                val request = mapCatProfileToUpdateRequest(cat)
                val response = apiService.updateCat(catId, request)
                if (response.isSuccess) {
                    // 重新获取完整信息
                    val fullCat = apiService.getCatDetail(catId)
                    mapApiResponseToCatProfile(fullCat)
                } else {
                    throw Exception("更新失败")
                }
            }
        )
    }

    /**
     * 删除猫咪档案
     */
    suspend fun deleteCat(catId: String): NetworkResult<Unit> {
        Log.d(TAG, "删除猫咪档案: $catId")
        return handleSingleResult(
            safeApiCall {
                val response = apiService.deleteCat(catId)
                Unit
            }
        )
    }

    /**
     * 获取猫咪统计数据（用于首页）
     */
    suspend fun getCatStatistics(): NetworkResult<List<CatStatistics>> {
        Log.d(TAG, "获取猫咪统计数据")
        return handleListResult(
            safeApiCall {
                // TODO: 实现实际的API调用
                // 暂时返回模拟数据
                generateMockCatStatistics()
            }
        )
    }

    /**
     * 获取猫咪警告信息（用于首页）
     */
    suspend fun getCatAlerts(): NetworkResult<List<CatAlert>> {
        Log.d(TAG, "获取猫咪警告信息")
        return handleListResult(
            safeApiCall {
                // TODO: 实现实际的API调用
                // 暂时返回模拟数据
                generateMockCatAlerts()
            }
        )
    }

    /**
     * 生成模拟的猫咪统计数据
     */
    private fun generateMockCatStatistics(): List<CatStatistics> {
        return listOf(
            CatStatistics(
                catId = "cat_1",
                catName = "小黑",
                avatarUrl = null,
                recentToiletCount = 3,
                recentTotalDuration = 480, // 8分钟
                previousToiletCount = 2,
                previousTotalDuration = 360, // 6分钟
                currentWeight = 4.2,
                previousWeight = 4.1,
                weightChange = 0.1,
                weightChangePercentage = 2.4
            ),
            CatStatistics(
                catId = "cat_2",
                catName = "小白",
                avatarUrl = null,
                recentToiletCount = 2,
                recentTotalDuration = 300, // 5分钟
                previousToiletCount = 3,
                previousTotalDuration = 420, // 7分钟
                currentWeight = 3.8,
                previousWeight = 3.9,
                weightChange = -0.1,
                weightChangePercentage = -2.6
            )
        )
    }

    /**
     * 生成模拟的猫咪警告信息
     */
    private fun generateMockCatAlerts(): List<CatAlert> {
        return listOf(
            // 暂时返回空列表，表示没有警告
        )
    }

    /**
     * 将API响应转换为本地模型
     */
    private fun mapApiResponseToCatProfile(apiResponse: CatAPIResponse): CatProfile {
        return CatProfile(
            id = apiResponse.catId,
            name = apiResponse.name,
            age = calculateAge(apiResponse.birthday),
            gender = apiResponse.gender,
            weight = if (apiResponse.weight > 0) "${apiResponse.weight}kg" else "未知",
            healthStatus = CatHealthStatus.HEALTHY.displayName,
            activityLevel = CatActivityLevel.NORMAL.displayName,
            type = CatType.WHITE.displayName,
            neuteredStatus = null,
            avatarUrl = apiResponse.avatarUrl,
            birthDate = apiResponse.birthday,
            lastCheckupDate = null,
            vaccinations = emptyList(),
            medications = emptyList(),
            dietaryRestrictions = emptyList()
        )
    }

    /**
     * 计算年龄
     */
    private fun calculateAge(birthday: String?): String {
        if (birthday.isNullOrEmpty()) return "未知年龄"

        return try {
            calculateAgeFromBirthDate(birthday)
        } catch (e: Exception) {
            Log.w(TAG, "计算年龄失败: $birthday", e)
            "未知年龄"
        }
    }

    /**
     * 解析日期
     */
    private fun parseDate(dateString: String?): java.util.Date? {
        if (dateString.isNullOrEmpty()) return null

        return try {
            // 这里应该实现实际的日期解析逻辑
            // 暂时返回null
            null
        } catch (e: Exception) {
            Log.w(TAG, "解析日期失败: $dateString", e)
            null
        }
    }

    /**
     * 将本地模型转换为创建请求
     */
    private fun mapCatProfileToCreateRequest(catProfile: CatProfile): CatProfile {
        // 暂时直接返回CatProfile，等API更新后再修改
        return catProfile
    }

    /**
     * 将本地模型转换为更新请求
     */
    private fun mapCatProfileToUpdateRequest(catProfile: CatProfile): CatProfile {
        // 暂时直接返回CatProfile，等API更新后再修改
        return catProfile
    }
}
