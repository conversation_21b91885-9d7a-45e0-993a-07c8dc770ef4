# 设备名称同步解决方案

## 问题描述

在iOS应用中，第二个页面（视频页面）和第四个页面（设备页面）显示的设备名称使用了不同的变量源：

- **第二个页面（视频页面）**：使用 `VideoSegment.deviceName` 属性
- **第四个页面（设备页面）**：使用 `DeviceStatusResponse.name` 或 `Device.name` 属性

当用户在设备页面更新设备名称时，视频页面的设备名称不会自动同步更新，导致显示不一致。

## 解决方案

### 1. 通知机制

在 `Extensions.swift` 中添加了新的通知名称：

```swift
/// 设备名称更新通知 - 用于同步视频页面和设备页面的设备名称显示
static let deviceNameUpdated = Notification.Name("deviceNameUpdated")
```

### 2. DeviceAndGroupManager 修改

在 `DeviceAndGroupManager.updateDeviceBasicInfo` 方法中：

1. 更新本地设备列表中的设备信息
2. 发送设备名称更新通知

```swift
// 🔄 同步更新VideoSegment中的设备名称
if let newName = name {
    await syncDeviceNameToVideoSegments(deviceId: deviceId, newName: newName)
}
```

通知发送方法：

```swift
private func syncDeviceNameToVideoSegments(deviceId: String, newName: String) async {
    let userInfo: [String: Any] = [
        "deviceId": deviceId,
        "newName": newName
    ]
    
    NotificationCenter.default.post(
        name: .deviceNameUpdated,
        object: nil,
        userInfo: userInfo
    )
    
    Log.info("🔄 已发送设备名称更新通知: \(deviceId) -> \(newName)")
}
```

### 3. DeviceManager 修改

在 `DeviceManager` 中：

1. 在初始化方法中添加通知监听
2. 添加处理设备名称更新的方法
3. 在析构方法中移除通知监听

```swift
/// 初始化方法 - 设置通知监听
init() {
    NotificationCenter.default.addObserver(
        self,
        selector: #selector(handleDeviceNameUpdated(_:)),
        name: .deviceNameUpdated,
        object: nil
    )
}

/// 处理设备名称更新通知
@objc private func handleDeviceNameUpdated(_ notification: Notification) {
    guard let userInfo = notification.userInfo,
          let deviceId = userInfo["deviceId"] as? String,
          let newName = userInfo["newName"] as? String else {
        return
    }
    
    Task { @MainActor in
        await self.updateDeviceNameInSegments(deviceId: deviceId, newName: newName)
    }
}
```

设备名称更新方法：

```swift
/// 更新指定设备的所有VideoSegment中的设备名称
@MainActor
func updateDeviceNameInSegments(deviceId: String, newName: String) async {
    guard var segments = deviceSegments[deviceId] else {
        return
    }
    
    // 更新所有该设备的视频片段中的设备名称
    for i in 0..<segments.count {
        segments[i].updateDeviceName(newName)
    }
    
    // 保存更新后的片段
    deviceSegments[deviceId] = segments
    
    // 同时更新devices数组中的设备名称
    if let index = devices.firstIndex(where: { $0.deviceId == deviceId }) {
        // 创建新的Device实例，更新name属性
        let updatedDevice = devices[index]
        let newDevice = Device(
            deviceId: updatedDevice.deviceId,
            userId: updatedDevice.userId,
            hardwareSn: updatedDevice.hardwareSn,
            name: newName,
            // ... 其他属性保持不变
        )
        devices[index] = newDevice
    }
}
```

## 工作流程

1. 用户在第四个页面（设备页面）修改设备名称
2. `DeviceAndGroupManager.updateDeviceBasicInfo` 被调用
3. API请求成功后，更新本地设备列表
4. 发送 `deviceNameUpdated` 通知
5. `DeviceManager` 接收到通知
6. 更新所有缓存的 `VideoSegment` 中的 `deviceName` 属性
7. 第二个页面（视频页面）显示更新后的设备名称

## 优势

1. **解耦合**：两个Manager之间通过通知机制通信，避免直接依赖
2. **实时同步**：设备名称更新后立即同步到视频页面
3. **数据一致性**：确保应用中所有地方显示的设备名称都是最新的
4. **可扩展性**：其他组件也可以监听设备名称更新通知

## 测试建议

1. 在设备页面修改设备名称
2. 切换到视频页面，验证设备名称是否已更新
3. 重启应用，验证设备名称是否持久化
4. 测试多个设备的名称更新是否都能正确同步
