import SwiftUI

struct NotificationSettingsView: View {
    @ObservedObject private var notificationManager = NotificationManager.shared
    @ObservedObject private var authManager = AuthManager.shared
    @State private var showLogoutAlert = false
    @State private var showQuietTimeSheet = false
    @State private var showTokenExpiredAlert = false
    @State private var tokenExpiryMessage = ""

    var body: some View {
        List {
            // MARK: - 通知类型设置
            Section {
                NavigationLink(destination: NotificationTypesView()) {
                    HStack {
                        Image(systemName: "bell.badge")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        Text(NSLocalizedString("notification_settings_title", comment: ""))
                        Spacer()
                        if notificationManager.isDailyNotificationsEnabled || notificationManager.isStatsNotificationsEnabled {
                            Text("\(notificationManager.getEnabledNotificationsCount()) \(NSLocalizedString("notification_enabled_count", comment: ""))")
                                .foregroundColor(.secondary)
                                .font(.subheadline)
                        }
                    }
                }

                // MARK: - 免打扰时间设置
                Button(action: { showQuietTimeSheet = true }) {
                    HStack {
                        Image(systemName: "moon.fill")
                            .foregroundColor(.purple)
                            .frame(width: 30)
                        Text(NSLocalizedString("notification_quiet_time", comment: ""))
                        Spacer()
                        Text(notificationManager.getQuietTimeDescription())
                            .foregroundColor(.secondary)
                            .font(.subheadline)
                    }
                }
            } header: {
                Text(LocalizedStringKey("notification_settings_title"))
            } footer: {
                Text(LocalizedStringKey("notification_settings_footer"))
            }
            


            // MARK: - 账号安全
            Section {
                Button(action: { showLogoutAlert = true }) {
                    HStack {
                        Image(systemName: "rectangle.portrait.and.arrow.right")
                            .foregroundColor(.red)
                            .frame(width: 30)
                        Text(LocalizedStringKey("settings_logout"))
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .navigationTitle(NSLocalizedString("settings_title", comment: ""))
        .sheet(isPresented: $showQuietTimeSheet) {
            QuietTimeSettingView(
                startHour: notificationManager.quietTimeStart / 60,
                startMinute: notificationManager.quietTimeStart % 60,
                endHour: notificationManager.quietTimeEnd / 60,
                endMinute: notificationManager.quietTimeEnd % 60
            ) { startMinutes, endMinutes in
                notificationManager.quietTimeStart = startMinutes
                notificationManager.quietTimeEnd = endMinutes
                notificationManager.saveSettings()
            }
        }
        .alert(NSLocalizedString("settings_logout_message", comment: ""), isPresented: $showLogoutAlert) {
            Button(NSLocalizedString("settings_logout_cancel", comment: ""), role: .cancel) { }
            Button(NSLocalizedString("settings_logout_confirm", comment: ""), role: .destructive) {
                authManager.logout()
            }
        } message: {
            Text(NSLocalizedString("settings_logout_message", comment: ""))
        }
        .alert(NSLocalizedString("settings_logout_message", comment: ""), isPresented: $showTokenExpiredAlert) {
            Button(NSLocalizedString("settings_logout_cancel", comment: ""), role: .cancel) { }
        } message: {
            Text(tokenExpiryMessage)
        }
        .task {
            if authManager.isAuthenticated {
                await notificationManager.syncNotificationSettings()
            }
        }
    }
}

// MARK: - 通知类型设置视图
struct NotificationTypesView: View {
    @ObservedObject private var notificationManager = NotificationManager.shared

    var body: some View {
        List {
            Section {
                // 日常通知开关
                Toggle(isOn: $notificationManager.isDailyNotificationsEnabled) {
                    HStack {
                        Image(systemName: "bell.badge")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        VStack(alignment: .leading) {
                            Text(NSLocalizedString("notification_daily_reminder", comment: ""))
                            Text(NSLocalizedString("notification_daily_reminder_description", comment: ""))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                // 统计通知开关
                Toggle(isOn: $notificationManager.isStatsNotificationsEnabled) {
                    HStack {
                        Image(systemName: "chart.bar")
                            .foregroundColor(.green)
                            .frame(width: 30)
                        VStack(alignment: .leading) {
                            Text(NSLocalizedString("notification_statistics_alert", comment: ""))
                            Text(NSLocalizedString("notification_weekly_reports", comment: ""))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }

            Section {
                // 异常通知（不可关闭）
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.red)
                        .frame(width: 30)
                    VStack(alignment: .leading) {
                        Text(NSLocalizedString("notification_abnormal_alert", comment: ""))
                        Text(NSLocalizedString("notification_abnormal_description", comment: ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Text(NSLocalizedString("notification_always_on", comment: ""))
                        .foregroundColor(.secondary)
                }

                // 健康通知（不可关闭）
                HStack {
                    Image(systemName: "heart")
                        .foregroundColor(.pink)
                        .frame(width: 30)
                    VStack(alignment: .leading) {
                        Text(NSLocalizedString("notification_health_alert", comment: ""))
                        Text(NSLocalizedString("notification_health_description", comment: ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Text(NSLocalizedString("notification_always_on", comment: ""))
                        .foregroundColor(.secondary)
                }

                // 维护通知（不可关闭）
                HStack {
                    Image(systemName: "wrench.and.screwdriver")
                        .foregroundColor(.orange)
                        .frame(width: 30)
                    VStack(alignment: .leading) {
                        Text(NSLocalizedString("notification_maintenance_alert", comment: ""))
                        Text(NSLocalizedString("notification_maintenance_description", comment: ""))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Text(NSLocalizedString("notification_always_on", comment: ""))
                        .foregroundColor(.secondary)
                }
            } header: {
                Text(NSLocalizedString("notification_important_title", comment: ""))
            } footer: {
                Text(NSLocalizedString("notification_health_safety_notice", comment: ""))
            }
        }
        .navigationTitle(NSLocalizedString("notification_types_title", comment: ""))
    }
}

// MARK: - 免打扰时间设置视图
struct QuietTimeSettingView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var startHour: Int
    @State private var startMinute: Int
    @State private var endHour: Int
    @State private var endMinute: Int
    @State private var startDate: Date
    @State private var endDate: Date
    let onSave: (Int, Int) -> Void

    private var is24Hour: Bool {
        DateFormatter.dateFormat(fromTemplate: "j", options: 0, locale: .current)?.contains("H") ?? false
    }

    init(startHour: Int, startMinute: Int, endHour: Int, endMinute: Int, onSave: @escaping (Int, Int) -> Void) {
        _startHour = State(initialValue: startHour)
        _startMinute = State(initialValue: startMinute)
        _endHour = State(initialValue: endHour)
        _endMinute = State(initialValue: endMinute)
        self.onSave = onSave

        // 初始化日期，包含小时和分钟
        let calendar = Calendar.current
        let now = Date()
        let startComponents = DateComponents(hour: startHour, minute: startMinute)
        let endComponents = DateComponents(hour: endHour, minute: endMinute)
        _startDate = State(initialValue: calendar.date(from: startComponents) ?? now)
        _endDate = State(initialValue: calendar.date(from: endComponents) ?? now)
    }

    var body: some View {
        NavigationView {
            Form {
                Section {
                    HStack {
                        Image(systemName: "moon.fill")
                            .foregroundColor(.purple)
                            .frame(width: 30)
                        DatePicker(
                            NSLocalizedString("notification_start_time", comment: ""),
                            selection: $startDate,
                            displayedComponents: .hourAndMinute
                        )
                        #if os(iOS)
                        #if swift(>=5.9)
                        .onChange(of: startDate) { oldValue, newValue in
                            let calendar = Calendar.current
                            startHour = calendar.component(.hour, from: newValue)
                            startMinute = calendar.component(.minute, from: newValue)
                        }
                        #else
                        .onChange(of: startDate) { newValue in
                            let calendar = Calendar.current
                            startHour = calendar.component(.hour, from: newValue)
                            startMinute = calendar.component(.minute, from: newValue)
                        }
                        #endif
                        #endif
                    }

                    HStack {
                        Image(systemName: "sun.max.fill")
                            .foregroundColor(.orange)
                            .frame(width: 30)
                        DatePicker(
                            NSLocalizedString("notification_end_time", comment: ""),
                            selection: $endDate,
                            displayedComponents: .hourAndMinute
                        )
                        #if os(iOS)
                        #if swift(>=5.9)
                        .onChange(of: endDate) { oldValue, newValue in
                            let calendar = Calendar.current
                            endHour = calendar.component(.hour, from: newValue)
                            endMinute = calendar.component(.minute, from: newValue)
                        }
                        #else
                        .onChange(of: endDate) { newValue in
                            let calendar = Calendar.current
                            endHour = calendar.component(.hour, from: newValue)
                            endMinute = calendar.component(.minute, from: newValue)
                        }
                        #endif
                        #endif
                    }
                } header: {
                    Text(NSLocalizedString("notification_quiet_time", comment: ""))
                }

                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text(NSLocalizedString("notification_current_setting", comment: ""))
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Spacer()
                        }

                        HStack {
                            Image(systemName: "bell.slash.fill")
                                .foregroundColor(.purple)
                            Text(timeRangeDescription)
                                .foregroundColor(.primary)
                        }
                        .padding(.vertical, 4)
                    }
                } footer: {
                    Text(NSLocalizedString("notification_quiet_time_footer", comment: ""))
                }
            }
            .navigationTitle(NSLocalizedString("notification_quiet_time", comment: ""))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button(NSLocalizedString("notification_cancel", comment: "")) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button(NSLocalizedString("notification_save", comment: "")) {
                        // 转换为分钟
                        let startTotalMinutes = startHour * 60 + startMinute
                        let endTotalMinutes = endHour * 60 + endMinute
                        onSave(startTotalMinutes, endTotalMinutes)
                        dismiss()
                    }
                }
            }
        }
    }

    // 格式化时间范围描述
    private var timeRangeDescription: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none

        let startTime = formatter.string(from: startDate)
        let endTime = formatter.string(from: endDate)

        return "\(startTime) - \(endTime)"
    }
}

// MARK: - NotificationManager Extension
extension NotificationManager {
    func getEnabledNotificationsCount() -> Int {
        var count = 0
        if isDailyNotificationsEnabled { count += 1 }
        if isStatsNotificationsEnabled { count += 1 }
        return count
    }

    func getQuietTimeDescription() -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.dateStyle = .none

        let calendar = Calendar.current
        let startDate = calendar.date(from: DateComponents(hour: quietTimeStart / 60, minute: quietTimeStart % 60)) ?? Date()
        let endDate = calendar.date(from: DateComponents(hour: quietTimeEnd / 60, minute: quietTimeEnd % 60)) ?? Date()

        let startTime = formatter.string(from: startDate)
        let endTime = formatter.string(from: endDate)

        return "\(startTime) - \(endTime)"
    }
}
