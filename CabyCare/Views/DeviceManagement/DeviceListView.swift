import SwiftUI

/// 设备列表视图
struct DeviceListView: View {
    @Binding var currentDevice: DeviceStatusResponse?
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var otaStatusManager = DeviceOTAStatusManager.shared

    let devices: [DeviceStatusResponse]
    let isLoading: Bool
    let isAutoRefreshing: Bool
    let onRefreshDeviceStatus: ((String) async -> Void)?
    let onDeviceTap: ((DeviceStatusResponse) -> Void)?

    init(currentDevice: Binding<DeviceStatusResponse?>,
         devices: [DeviceStatusResponse],
         isLoading: Bool,
         isAutoRefreshing: Bool = false,
         onRefreshDeviceStatus: ((String) async -> Void)? = nil,
         onDeviceTap: ((DeviceStatusResponse) -> Void)? = nil) {
        self._currentDevice = currentDevice
        self.devices = devices
        self.isLoading = isLoading
        self.isAutoRefreshing = isAutoRefreshing
        self.onRefreshDeviceStatus = onRefreshDeviceStatus
        self.onDeviceTap = onDeviceTap
    }
    
    var body: some View {
        List {
            Section(header: headerView) {
                if devices.isEmpty && isLoading {
                    // 只有设备列表为空且正在加载时才显示loading
                    HStack {
                        Spacer()
                        ProgressView("正在加载设备状态...")
                            .font(.subheadline)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        Spacer()
                    }
                    .padding(.vertical)
                } else if devices.isEmpty {
                    // 设备列表为空且不在加载状态
                    HStack {
                        Spacer()
                        VStack(spacing: 8) {
                            Image(systemName: "video.slash")
                                .font(.title2)
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme).opacity(0.6))
                            Text("暂无设备")
                                .font(.subheadline)
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            Text("请使用右上角的加号添加设备")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                        Spacer()
                    }
                    .padding(.vertical)
                } else {
                    // 有设备时始终显示设备列表
                    ForEach(devices, id: \.id) { device in
                        DeviceRow(
                            device: device,
                            isSelected: currentDevice?.id == device.id,
                            onRefreshStatus: onRefreshDeviceStatus,
                            isAutoRefreshing: isAutoRefreshing
                        )
                        .onTapGesture {
                            // 如果提供了设备点击回调，直接调用进入详情页面
                            if let onDeviceTap = onDeviceTap {
                                onDeviceTap(device)
                            } else {
                                // 否则保持原有的选中逻辑（向后兼容）
                                currentDevice = device
                            }
                        }
                    }
                    
                    // 如果有设备且正在加载，在底部显示一个小的刷新指示器
                    if isLoading {
                        HStack {
                            Spacer()
                            HStack(spacing: 8) {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("正在更新设备状态...")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            }
                            Spacer()
                        }
                        .padding(.vertical, 8)
                    }
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
    }
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text("我的设备")
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                if !devices.isEmpty {
                    let onlineCount = devices.filter { $0.online == true }.count
                    let unknownCount = devices.filter { $0.online == nil }.count
                    let totalCount = devices.count
                    let updatingCount = otaStatusManager.updatingDevicesCount
                    
                    HStack(spacing: 4) {
                        Text("\(totalCount) 台设备")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        
                        Text("\(onlineCount) 台在线")
                            .font(.caption)
                            .foregroundColor(onlineCount > 0 ? .green : AppTheme.secondaryTextColor(for: colorScheme))
                        
                        // 如果有未知状态的设备，显示未知计数
                        if unknownCount > 0 {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            
                            Text("\(unknownCount) 台状态未知")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        
                        // OTA升级状态统计
                        if updatingCount > 0 {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            
                            HStack(spacing: 2) {
                                Image(systemName: "arrow.clockwise.circle.fill")
                                    .font(.caption2)
                                    .foregroundColor(.blue)
                                    .rotationEffect(.degrees(360))
                                    .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: true)
                                
                                Text("\(updatingCount) 台升级中")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .fontWeight(.medium)
                            }
                        }
                        
                        if isAutoRefreshing {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            
                            HStack(spacing: 2) {
                                Image(systemName: "arrow.clockwise")
                                    .font(.caption2)
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                    .rotationEffect(.degrees(isAutoRefreshing ? 360 : 0))
                                    .animation(
                                        isAutoRefreshing ? 
                                            .linear(duration: 2).repeatForever(autoreverses: false) : 
                                            .default,
                                        value: isAutoRefreshing
                                    )
                                
                                Text("自动刷新中")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            }
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview
struct DeviceListView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            // 有设备的情况
            DeviceListView(
                currentDevice: .constant(DeviceStatusResponse(
                    id: "device1",
                    name: "客厅猫厕所",
                    model: "CabyPro 2023",
                    firmware: "1.2.3",
                    online: true
                )),
                devices: [
                    DeviceStatusResponse(
                        id: "device1",
                        name: "客厅猫厕所",
                        model: "CabyPro 2023",
                        firmware: "1.2.3",
                        online: true,
                        lastHeartbeat: "2023-06-01T15:30:00Z"
                    ),
                    DeviceStatusResponse(
                        id: "device2",
                        name: "卧室猫厕所",
                        model: "CabyLite 2023",
                        firmware: "1.1.0",
                        online: false,
                        lastHeartbeat: "2023-06-01T12:00:00Z"
                    )
                ],
                isLoading: false,
                isAutoRefreshing: true,
                onRefreshDeviceStatus: { _ in
                    try? await Task.sleep(nanoseconds: 1_000_000_000)
                }
            )
        }
    }
} 