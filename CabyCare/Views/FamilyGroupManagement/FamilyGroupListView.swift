import SwiftUI

/// 家庭组列表视图
struct FamilyGroupListView: View {
    @Binding var currentGroup: FamilyGroup?
    @Environment(\.colorScheme) var colorScheme
    @State private var showCreateGroup = false
    @State private var showInvitations = false
    @State private var showInviteUser = false

    @ObservedObject var manager: DeviceAndGroupManager
    let onGroupTap: ((FamilyGroup) -> Void)?

    init(currentGroup: Binding<FamilyGroup?>,
         manager: DeviceAndGroupManager,
         onGroupTap: ((FamilyGroup) -> Void)? = nil) {
        self._currentGroup = currentGroup
        self.manager = manager
        self.onGroupTap = onGroupTap
    }
    
    var body: some View {
        List {
            Section(header: Text("我的家庭组").foregroundColor(AppTheme.textColor(for: colorScheme))) {
                if manager.isFetchingGroups && manager.familyGroups.isEmpty {
                    HStack {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryColor(for: colorScheme)))
                        Text("加载中...")
                            .font(.caption)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            .padding(.leading, 8)
                        Spacer()
                    }
                    .padding(.vertical, 16)
                } else if manager.familyGroups.isEmpty {
                    // 显示空状态
                    VStack(spacing: 12) {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
                            .frame(width: 64, height: 64)
                            .overlay(
                                Image(systemName: "person.2.badge.plus")
                                    .font(.system(size: 28))
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            )
                        
                        VStack(spacing: 4) {
                            Text("暂无家庭组")
                                .font(.headline)
                                .foregroundColor(AppTheme.textColor(for: colorScheme))
                            
                            Text("创建家庭组来与家人共享设备")
                                .font(.subheadline)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 32)
                    .listRowBackground(Color.clear)
                } else {
                    // 显示API获取的实际数据
                    ForEach(manager.familyGroups) { group in
                        FamilyGroupRow(group: group)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                // 如果提供了家庭组点击回调，直接调用进入详情页面
                                if let onGroupTap = onGroupTap {
                                    onGroupTap(group)
                                } else {
                                    // 否则保持原有的选中逻辑（向后兼容）
                                    currentGroup = group
                                }
                            }
                    }
                }
            }
            
            Section(header: Text("邀请").foregroundColor(AppTheme.textColor(for: colorScheme))) {
                Button(action: {
                    showInvitations = true
                }) {
                    HStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
                            .frame(width: 32, height: 32)
                            .overlay(
                                Image(systemName: "envelope")
                                    .font(.system(size: 16))
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            )
                        
                        Text("查看邀请")
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        
                        Spacer()
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .listStyle(InsetGroupedListStyle())
        .background(Color(.systemGroupedBackground))
        .sheet(isPresented: $showCreateGroup) {
            CreateFamilyGroupView(manager: manager)
        }
        .sheet(isPresented: $showInvitations) {
            InvitationsListView(manager: manager)
        }
        .sheet(isPresented: $showInviteUser) {
            CreateInvitationView(groups: manager.familyGroups)
        }
        .onAppear {
            // 自动获取家庭组数据
            Task {
                await manager.fetchFamilyGroups()
            }
        }
    }
} 