import SwiftUI
import Charts

// MARK: - Weight Line Chart
struct WeightLineChart: View {
    let chartData: CatChartData
    let configuration: ChartConfiguration
    @State private var selectedDataPoint: WeightChartDataPoint?
    @State private var animationProgress: Double = 0
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 16) {
            // 图表标题和统计信息
            chartHeader
            
            // 主要折线图
            mainChart
                .frame(height: 200)
            
            // 图表底部信息
            chartFooter
        }
        .onAppear {
            if configuration.animationEnabled {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animationProgress = 1.0
                }
            } else {
                animationProgress = 1.0
            }
        }
    }
    
    // MARK: - Chart Header
    private var chartHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("重量趋势")
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                if let selectedPoint = selectedDataPoint {
                    Text("\(selectedPoint.formattedDate): \(selectedPoint.formattedWeight)")
                        .font(.subheadline)
                        .foregroundColor(configuration.primaryColor)
                } else {
                    Text("平均重量: \(String(format: "%.1fkg", chartData.averageWeight))")
                        .font(.subheadline)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
            }
            
            Spacer()
            
            // 重量范围显示
            VStack(alignment: .trailing, spacing: 4) {
                Text("范围")
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                
                Text("\(String(format: "%.1f", chartData.minWeight)) - \(String(format: "%.1f", chartData.maxWeight))kg")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
            }
        }
    }
    
    // MARK: - Main Chart
    private var mainChart: some View {
        Chart {
            ForEach(Array(chartData.weightDataPoints.enumerated()), id: \.element.id) { index, dataPoint in
                // 只显示有数据的点
                if dataPoint.averageWeight > 0 {
                    // 折线
                    LineMark(
                        x: .value("日期", dataPoint.date),
                        y: .value("重量", dataPoint.averageWeight)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [configuration.primaryColor, configuration.primaryColor.opacity(0.7)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                    .interpolationMethod(.catmullRom)
                    
                    // 数据点
                    if configuration.showDataPoints {
                        PointMark(
                            x: .value("日期", dataPoint.date),
                            y: .value("重量", dataPoint.averageWeight)
                        )
                        .foregroundStyle(configuration.primaryColor)
                        .symbolSize(selectedDataPoint?.id == dataPoint.id ? 100 : 64)
                        .opacity(animationProgress)
                    }
                    
                    // 面积填充
                    AreaMark(
                        x: .value("日期", dataPoint.date),
                        yStart: .value("基线", yAxisDomain.lowerBound),
                        yEnd: .value("重量", dataPoint.averageWeight)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [
                                configuration.primaryColor.opacity(0.3),
                                configuration.primaryColor.opacity(0.1),
                                Color.clear
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .opacity(animationProgress)
                    
                    // 选中点的垂直线
                    if let selectedPoint = selectedDataPoint,
                       selectedPoint.id == dataPoint.id {
                        RuleMark(x: .value("日期", dataPoint.date))
                            .foregroundStyle(configuration.primaryColor.opacity(0.5))
                            .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                    }
                }
            }
        }
        .chartBackground { chartProxy in
            GeometryReader { geometry in
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .onTapGesture { location in
                        handleChartTap(at: location, in: geometry, chartProxy: chartProxy)
                    }
            }
        }
        .chartYScale(domain: yAxisDomain)
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.3))
                
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        Text(formatAxisDate(date))
                            .font(.caption2)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(values: .automatic) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.3))
                
                AxisValueLabel {
                    if let weight = value.as(Double.self) {
                        Text("\(String(format: "%.1f", weight))kg")
                            .font(.caption2)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                }
            }
        }
    }
    
    // MARK: - Chart Footer
    private var chartFooter: some View {
        HStack {
            // 数据点数量
            Label("\(chartData.weightDataPoints.filter { $0.averageWeight > 0 }.count) 个数据点", 
                  systemImage: "circle.fill")
                .font(.caption)
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            
            Spacer()
            
            // 趋势指示器
            HStack(spacing: 4) {
                Image(systemName: chartData.trend.icon)
                    .font(.caption)
                    .foregroundColor(chartData.trend.color)
                
                Text(chartData.trend.description)
                    .font(.caption)
                    .foregroundColor(chartData.trend.color)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private var yAxisDomain: ClosedRange<Double> {
        let validWeights = chartData.weightDataPoints.compactMap { $0.averageWeight > 0 ? $0.averageWeight : nil }
        
        guard !validWeights.isEmpty else {
            return 0...10 // 默认范围
        }
        
        let minWeight = validWeights.min() ?? 0
        let maxWeight = validWeights.max() ?? 10
        
        // 添加一些边距
        let margin = (maxWeight - minWeight) * 0.1
        let adjustedMin = max(0, minWeight - margin)
        let adjustedMax = maxWeight + margin
        
        return adjustedMin...adjustedMax
    }
    
    private func formatAxisDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        switch chartData.timePeriod {
        case .day:
            formatter.dateFormat = "MM/dd"
        case .week:
            formatter.dateFormat = "MM/dd"
        case .month:
            formatter.dateFormat = "MM月"
        }
        
        return formatter.string(from: date)
    }
    
    private func handleChartTap(at location: CGPoint, in geometry: GeometryProxy, chartProxy: ChartProxy) {
        // 获取点击位置对应的日期
        let xPosition = location.x - geometry.frame(in: .local).minX

        if let date: Date = chartProxy.value(atX: xPosition) {
            // 找到最接近的数据点
            let validDataPoints = chartData.weightDataPoints.filter { $0.averageWeight > 0 }
            let closestPoint = validDataPoints.min { point1, point2 in
                abs(point1.date.timeIntervalSince(date)) < abs(point2.date.timeIntervalSince(date))
            }

            withAnimation(.easeInOut(duration: 0.3)) {
                if selectedDataPoint?.id == closestPoint?.id {
                    selectedDataPoint = nil
                } else {
                    selectedDataPoint = closestPoint
                }
            }
        }
    }
}

// MARK: - Preview
struct WeightLineChart_Previews: PreviewProvider {
    static var previews: some View {
        let sampleWeightData = [
            WeightChartDataPoint(date: Date().addingTimeInterval(-6*24*60*60), averageWeight: 4.2, minWeight: 4.1, maxWeight: 4.3, recordCount: 5),
            WeightChartDataPoint(date: Date().addingTimeInterval(-5*24*60*60), averageWeight: 4.3, minWeight: 4.2, maxWeight: 4.4, recordCount: 3),
            WeightChartDataPoint(date: Date().addingTimeInterval(-4*24*60*60), averageWeight: 4.1, minWeight: 4.0, maxWeight: 4.2, recordCount: 4),
            WeightChartDataPoint(date: Date().addingTimeInterval(-3*24*60*60), averageWeight: 4.4, minWeight: 4.3, maxWeight: 4.5, recordCount: 6),
            WeightChartDataPoint(date: Date().addingTimeInterval(-2*24*60*60), averageWeight: 4.2, minWeight: 4.1, maxWeight: 4.3, recordCount: 2),
            WeightChartDataPoint(date: Date().addingTimeInterval(-1*24*60*60), averageWeight: 4.5, minWeight: 4.4, maxWeight: 4.6, recordCount: 4),
            WeightChartDataPoint(date: Date(), averageWeight: 4.3, minWeight: 4.2, maxWeight: 4.4, recordCount: 3)
        ]
        
        let sampleData = CatChartData(
            catId: "1",
            catName: "小黑",
            avatarUrl: nil,
            weightDataPoints: sampleWeightData,
            timePeriod: .day,
            dateRange: DateRange.defaultRange(for: .day)
        )
        
        WeightLineChart(
            chartData: sampleData,
            configuration: ChartConfiguration.themed(for: .light)
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
