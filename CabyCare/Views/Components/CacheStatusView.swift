import SwiftUI

// MARK: - Cache Status View
struct CacheStatusView: View {
    @StateObject private var chartDataService = ChartDataService.shared
    @StateObject private var cacheManager = ChartCacheManager.shared
    @State private var statistics = CacheStatistics.empty
    @State private var isLoading = false
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            List {
                // 缓存统计信息
                Section("缓存统计") {
                    StatRow(title: "已缓存天数", value: "\(statistics.totalCachedDays)")
                    StatRow(title: "已缓存猫咪", value: "\(statistics.totalCachedCats)")
                    StatRow(title: "缓存大小", value: formatBytes(statistics.cacheSize))
                    StatRow(title: "缓存命中率", value: String(format: "%.1f%%", statistics.hitRate * 100))
                    
                    if let oldestDate = statistics.oldestCachedDate {
                        StatRow(title: "最早数据", value: formatDate(oldestDate))
                    }
                    
                    if let newestDate = statistics.newestCachedDate {
                        StatRow(title: "最新数据", value: formatDate(newestDate))
                    }
                }
                
                // 操作按钮
                Section("缓存操作") {
                    Button(action: {
                        Task {
                            await refreshStatistics()
                        }
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                                .foregroundColor(.blue)
                            Text("刷新统计")
                            Spacer()
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(isLoading)
                    
                    Button(action: cleanupCache) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.orange)
                            Text("清理过期数据")
                        }
                    }
                    .disabled(isLoading)
                    
                    Button(action: preloadData) {
                        HStack {
                            Image(systemName: "square.and.arrow.down")
                                .foregroundColor(.green)
                            Text("预加载数据")
                        }
                    }
                    .disabled(isLoading)
                }
                
                // 缓存配置信息
                Section("配置信息") {
                    StatRow(title: "最大缓存天数", value: "\(ChartCacheConfiguration.maxCacheDays)")
                    StatRow(title: "今日更新间隔", value: "\(Int(ChartCacheConfiguration.todayUpdateInterval / 60))分钟")
                    StatRow(title: "批量获取天数", value: "\(ChartCacheConfiguration.maxBatchDays)")
                    StatRow(title: "预加载历史天数", value: "\(ChartCacheConfiguration.preloadHistoryDays)")
                }
            }
            .navigationTitle("缓存状态")
            .navigationBarTitleDisplayMode(.inline)
            .refreshable {
                await refreshStatistics()
            }
        }
        .onAppear {
            Task {
                await refreshStatistics()
            }
        }
    }
    
    // MARK: - Actions
    
    private func refreshStatistics() async {
        isLoading = true

        let newStatistics = chartDataService.getCacheStatistics()
        statistics = newStatistics
        isLoading = false
    }
    
    private func cleanupCache() {
        isLoading = true
        
        Task {
            await chartDataService.cleanupCache()
            await MainActor.run {
                statistics = chartDataService.getCacheStatistics()
                isLoading = false
            }
        }
    }
    
    private func preloadData() {
        isLoading = true
        
        Task {
            let catManager = CatManager.shared
            let catIds = catManager.cats.map { $0.id }
            
            // 预加载所有时间段的数据
            await chartDataService.preloadDataForPeriod(.day, for: catIds)
            await chartDataService.preloadDataForPeriod(.week, for: catIds)
            await chartDataService.preloadDataForPeriod(.month, for: catIds)
            
            await MainActor.run {
                statistics = chartDataService.getCacheStatistics()
                isLoading = false
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }

    private func formatDateShort(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        return formatter.string(from: date)
    }
}

// MARK: - Supporting Views
private struct StatRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.primary)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Preview
struct CacheStatusView_Previews: PreviewProvider {
    static var previews: some View {
        CacheStatusView()
    }
}

// MARK: - Cache Test View
struct CacheTestView: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false
    
    var body: some View {
        NavigationView {
            VStack {
                // 测试结果
                List(testResults, id: \.self) { result in
                    Text(result)
                        .font(.system(.caption, design: .monospaced))
                }
                
                // 测试按钮
                Button(action: runTests) {
                    HStack {
                        if isRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "play.circle")
                        }
                        Text(isRunning ? "测试中..." : "运行缓存测试")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isRunning)
                .padding()
            }
            .navigationTitle("缓存测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func runTests() {
        isRunning = true
        testResults.removeAll()
        
        Task {
            await performCacheTests()
            await MainActor.run {
                isRunning = false
            }
        }
    }
    
    private func performCacheTests() async {
        let cacheManager = ChartCacheManager.shared
        let chartDataService = ChartDataService.shared

        addResultSync("🧪 开始缓存测试...")

        // 测试1: 基本缓存操作
        addResultSync("📝 测试1: 基本缓存操作")
        let testDate = Date()
        let testCatId = "test-cat-1"
        let testData = DailyToiletData(date: testDate, catId: testCatId, segments: [])

        cacheManager.saveDailyData(testData)

        if cacheManager.getDailyData(for: testCatId, date: testDate) != nil {
            addResultSync("✅ 缓存保存和读取成功")
        } else {
            addResultSync("❌ 缓存保存和读取失败")
        }

        // 测试2: 缺失日期检测
        addResultSync("📝 测试2: 缺失日期检测")
        let dateRange = DateRange(
            startDate: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
            endDate: Date()
        )
        let missingDates = cacheManager.getMissingDates(for: testCatId, in: dateRange)
        addResultSync("📅 发现 \(missingDates.count) 个缺失日期")

        // 测试3: 统计信息
        addResultSync("📝 测试3: 统计信息")
        let stats = chartDataService.getCacheStatistics()
        addResultSync("📊 缓存统计: \(stats.totalCachedDays) 天, \(stats.totalCachedCats) 只猫")

        // 测试4: 周数据范围计算
        addResultSync("📝 测试4: 周数据范围计算")
        await testWeekDataRange()

        // 测试5: 月数据范围计算
        addResultSync("📝 测试5: 月数据范围计算")
        await testMonthDataRange()

        addResultSync("✅ 所有测试完成")
    }

    private func testWeekDataRange() async {
        let calendar = Calendar.current
        let today = Date()

        // 测试周的开始和结束计算
        let weekStart = calendar.startOfWeek(for: today)
        let weekEnd = calendar.endOfWeek(for: today)

        addResult("📅 本周范围: \(formatDateShort(weekStart)) 到 \(formatDateShort(weekEnd))")

        // 测试8周前的计算
        let eightWeeksAgo = calendar.date(byAdding: .weekOfYear, value: -7, to: today) ?? today
        let eightWeeksStart = calendar.startOfWeek(for: eightWeeksAgo)

        addResult("📅 8周前开始: \(formatDateShort(eightWeeksStart))")

        // 验证是否为周一
        let weekday = calendar.component(.weekday, from: weekStart)
        let isMonday = weekday == 2 // 2 = 周一
        addResult(isMonday ? "✅ 周开始为周一" : "❌ 周开始不是周一")
    }

    private func testMonthDataRange() async {
        let calendar = Calendar.current
        let today = Date()

        // 测试月的开始和结束计算
        let monthStart = calendar.monthStart(for: today)
        let monthEnd = calendar.endOfMonth(for: today)

        addResult("📅 本月范围: \(formatDateShort(monthStart)) 到 \(formatDateShort(monthEnd))")

        // 测试8个月前的计算
        let eightMonthsAgo = calendar.date(byAdding: .month, value: -7, to: today) ?? today
        let eightMonthsStart = calendar.monthStart(for: eightMonthsAgo)

        addResult("📅 8个月前开始: \(formatDateShort(eightMonthsStart))")

        // 验证是否为月初
        let dayOfMonth = calendar.component(.day, from: monthStart)
        let isFirstDay = dayOfMonth == 1
        addResult(isFirstDay ? "✅ 月开始为1号" : "❌ 月开始不是1号")
    }
    
    @MainActor
    private func addResult(_ message: String) {
        testResults.append("[\(Date().formattedString())] \(message)")
    }

    private func addResultSync(_ message: String) {
        Task { @MainActor in
            testResults.append("[\(Date().formattedString())] \(message)")
        }
    }

    private func formatDateShort(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        return formatter.string(from: date)
    }
}

struct CacheTestView_Previews: PreviewProvider {
    static var previews: some View {
        CacheTestView()
    }
}

// MARK: - Week Month Data Test View
struct WeekMonthDataTestView: View {
    @State private var testResults: [String] = []
    @State private var isRunning = false

    var body: some View {
        NavigationView {
            VStack {
                // 测试结果
                List(testResults, id: \.self) { result in
                    Text(result)
                        .font(.system(.caption, design: .monospaced))
                }

                // 测试按钮
                Button(action: runWeekMonthTests) {
                    HStack {
                        if isRunning {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "calendar")
                        }
                        Text(isRunning ? "测试中..." : "测试周月数据")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isRunning)
                .padding()
            }
            .navigationTitle("周月数据测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func runWeekMonthTests() {
        isRunning = true
        testResults.removeAll()

        Task {
            await performWeekMonthTests()
            await MainActor.run {
                isRunning = false
            }
        }
    }

    private func performWeekMonthTests() async {
        addResult("📊 开始周月数据测试...")

        // 测试周数据计算
        addResult("📝 测试周数据计算")
        await testWeekCalculations()

        // 测试月数据计算
        addResult("📝 测试月数据计算")
        await testMonthCalculations()

        // 测试数据范围生成
        addResult("📝 测试数据范围生成")
        await testDataRangeGeneration()

        addResult("✅ 周月数据测试完成")
    }

    private func testWeekCalculations() async {
        let calendar = Calendar.current
        let today = Date()

        // 测试连续8周的计算
        var currentWeekStart = calendar.startOfWeek(for: calendar.date(byAdding: .weekOfYear, value: -7, to: today) ?? today)

        for weekIndex in 0..<8 {
            let weekEnd = calendar.endOfWeek(for: currentWeekStart)
            let weekdayStart = calendar.component(.weekday, from: currentWeekStart)
            let weekdayEnd = calendar.component(.weekday, from: weekEnd)

            let formatter = DateFormatter()
            formatter.dateFormat = "MM-dd"
            let startStr = formatter.string(from: currentWeekStart)
            let endStr = formatter.string(from: weekEnd)

            let isValidWeek = weekdayStart == 2 && weekdayEnd == 1 // 周一到周日
            let status = isValidWeek ? "✅" : "❌"

            addResult("\(status) 第\(weekIndex + 1)周: \(startStr) 到 \(endStr)")

            currentWeekStart = calendar.date(byAdding: .weekOfYear, value: 1, to: currentWeekStart) ?? currentWeekStart
        }
    }

    private func testMonthCalculations() async {
        let calendar = Calendar.current
        let today = Date()

        // 测试连续8个月的计算
        var currentMonthStart = calendar.monthStart(for: calendar.date(byAdding: .month, value: -7, to: today) ?? today)

        for monthIndex in 0..<8 {
            let _ = calendar.endOfMonth(for: currentMonthStart)
            let dayStart = calendar.component(.day, from: currentMonthStart)

            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            let monthStr = formatter.string(from: currentMonthStart)

            let isValidMonth = dayStart == 1
            let status = isValidMonth ? "✅" : "❌"

            addResult("\(status) 第\(monthIndex + 1)月: \(monthStr)")

            currentMonthStart = calendar.date(byAdding: .month, value: 1, to: currentMonthStart) ?? currentMonthStart
        }
    }

    private func testDataRangeGeneration() async {
        let _ = ChartDataService.shared

        // 测试不同时间段的数据范围
        let periods: [ChartTimePeriod] = [.day, .week, .month]

        for period in periods {
            // 这里我们需要访问私有方法，所以先跳过实际测试
            addResult("📊 \(period.rawValue) 时间段配置已更新")
        }
    }

    private func addResult(_ message: String) {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        let timeStr = formatter.string(from: Date())

        Task { @MainActor in
            testResults.append("[\(timeStr)] \(message)")
        }
    }
}

struct WeekMonthDataTestView_Previews: PreviewProvider {
    static var previews: some View {
        WeekMonthDataTestView()
    }
}
