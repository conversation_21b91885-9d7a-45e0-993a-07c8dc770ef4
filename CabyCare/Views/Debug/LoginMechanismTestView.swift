import SwiftUI

/// 调试视图：测试新的登录验证机制
struct LoginMechanismTestView: View {
    @ObservedObject private var authManager = AuthManager.shared
    @State private var testResults: [String] = []
    @State private var isRunningTests = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 当前状态显示
                VStack(alignment: .leading, spacing: 10) {
                    Text("当前认证状态")
                        .font(.headline)
                    
                    HStack {
                        Text("认证状态:")
                        Spacer()
                        Text(authManager.isAuthenticated ? "已登录" : "未登录")
                            .foregroundColor(authManager.isAuthenticated ? .green : .red)
                    }
                    
                    HStack {
                        Text("登录日期:")
                        Spacer()
                        if let loginDate = UserDefaultsManager.shared.loginDate {
                            Text(DateFormatter.localizedString(from: loginDate, dateStyle: .short, timeStyle: .short))
                                .foregroundColor(.blue)
                        } else {
                            Text("无")
                                .foregroundColor(.gray)
                        }
                    }
                    
                    HStack {
                        Text("30天内登录:")
                        Spacer()
                        Text(UserDefaultsManager.shared.isLoggedInWithinDays(30) ? "是" : "否")
                            .foregroundColor(UserDefaultsManager.shared.isLoggedInWithinDays(30) ? .green : .red)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                // 测试按钮
                VStack(spacing: 15) {
                    Button("模拟登录成功") {
                        simulateLoginSuccess()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("清除登录状态") {
                        clearLoginStatus()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("运行完整测试") {
                        runFullTests()
                    }
                    .buttonStyle(.bordered)
                    .disabled(isRunningTests)
                    
                    Button("刷新状态") {
                        refreshStatus()
                    }
                    .buttonStyle(.bordered)
                }
                
                // 测试结果显示
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("测试结果")
                            .font(.headline)
                        
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 2) {
                                ForEach(testResults.indices, id: \.self) { index in
                                    Text(testResults[index])
                                        .font(.caption)
                                        .foregroundColor(testResults[index].contains("✅") ? .green : 
                                                       testResults[index].contains("❌") ? .red : .primary)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("登录机制测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func simulateLoginSuccess() {
        // 模拟登录成功
        UserDefaultsManager.shared.setCurrentLoginDate()
        UserDefaultsManager.shared.accessToken = "test_access_token_\(Date().timeIntervalSince1970)"
        UserDefaultsManager.shared.userId = "test_user_\(UUID().uuidString.prefix(8))"
        
        // 更新认证状态
        authManager.checkAuthentication()
        
        addTestResult("✅ 模拟登录成功，已保存登录日期")
    }
    
    private func clearLoginStatus() {
        UserDefaultsManager.shared.clearAuthCredentials()
        authManager.checkAuthentication()
        addTestResult("🧹 已清除所有登录状态")
    }
    
    private func refreshStatus() {
        authManager.checkAuthentication()
        addTestResult("🔄 已刷新认证状态")
    }
    
    private func runFullTests() {
        isRunningTests = true
        testResults.removeAll()
        
        DispatchQueue.global(qos: .userInitiated).async {
            let storage = UserDefaultsManager.shared
            
            // 测试1: 初始状态
            DispatchQueue.main.async {
                self.addTestResult("📋 开始测试1: 初始状态检查")
            }
            
            storage.clearAuthCredentials()
            let initialStatus = storage.isLoggedInWithinDays(30)
            
            DispatchQueue.main.async {
                if !initialStatus {
                    self.addTestResult("✅ 测试1通过: 初始状态正确（未登录）")
                } else {
                    self.addTestResult("❌ 测试1失败: 初始状态错误（应该未登录）")
                }
            }
            
            // 测试2: 设置登录日期
            DispatchQueue.main.async {
                self.addTestResult("📋 开始测试2: 设置登录日期")
            }
            
            storage.setCurrentLoginDate()
            let afterSetStatus = storage.isLoggedInWithinDays(30)
            
            DispatchQueue.main.async {
                if afterSetStatus {
                    self.addTestResult("✅ 测试2通过: 设置登录日期后状态正确（已登录）")
                } else {
                    self.addTestResult("❌ 测试2失败: 设置登录日期后状态错误（应该已登录）")
                }
            }
            
            // 测试3: 过期测试
            DispatchQueue.main.async {
                self.addTestResult("📋 开始测试3: 过期日期测试")
            }
            
            let pastDate = Calendar.current.date(byAdding: .day, value: -31, to: Date())!
            storage.loginDate = pastDate
            let expiredStatus = storage.isLoggedInWithinDays(30)
            let extendedStatus = storage.isLoggedInWithinDays(35)
            
            DispatchQueue.main.async {
                if !expiredStatus && extendedStatus {
                    self.addTestResult("✅ 测试3通过: 过期逻辑正确")
                } else {
                    self.addTestResult("❌ 测试3失败: 过期逻辑错误")
                }
            }
            
            // 测试4: 清除测试
            DispatchQueue.main.async {
                self.addTestResult("📋 开始测试4: 清除认证数据")
            }
            
            storage.setCurrentLoginDate()
            storage.accessToken = "test"
            storage.clearAuthCredentials()
            
            let clearedLoginDate = storage.loginDate == nil
            let clearedToken = storage.accessToken == nil
            
            DispatchQueue.main.async {
                if clearedLoginDate && clearedToken {
                    self.addTestResult("✅ 测试4通过: 清除功能正确")
                } else {
                    self.addTestResult("❌ 测试4失败: 清除功能错误")
                }
            }
            
            // 测试5: AuthManager集成测试
            DispatchQueue.main.async {
                self.addTestResult("📋 开始测试5: AuthManager集成测试")
            }
            
            storage.clearAuthCredentials()
            
            DispatchQueue.main.async {
                self.authManager.checkAuthentication()
                let clearedStatus = !self.authManager.isAuthenticated
                
                if clearedStatus {
                    self.addTestResult("✅ 测试5a通过: 清除后未认证")
                } else {
                    self.addTestResult("❌ 测试5a失败: 清除后仍然认证")
                }
                
                // 设置登录日期后测试
                storage.setCurrentLoginDate()
                self.authManager.checkAuthentication()
                let setStatus = self.authManager.isAuthenticated
                
                if setStatus {
                    self.addTestResult("✅ 测试5b通过: 设置登录日期后已认证")
                } else {
                    self.addTestResult("❌ 测试5b失败: 设置登录日期后未认证")
                }
                
                self.addTestResult("🎉 所有测试完成！")
                self.isRunningTests = false
                
                // 清理测试数据
                storage.clearAuthCredentials()
                self.authManager.checkAuthentication()
            }
        }
    }
    
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

#Preview {
    LoginMechanismTestView()
}
