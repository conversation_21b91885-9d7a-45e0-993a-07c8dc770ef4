import Foundation

// MARK: - Chart Cache Manager
@MainActor
class ChartCacheManager: ObservableObject {
    static let shared = ChartCacheManager()
    
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    
    // 内存缓存
    private var dailyDataCache: [String: [String: DailyToiletData]] = [:] // [catId: [date: data]]
    private var cacheStatusMap: [String: CacheStatus] = [:] // [catId: status]
    
    // 统计信息
    @Published var statistics = CacheStatistics.empty
    private var cacheHits = 0
    private var cacheMisses = 0
    
    private init() {
        // 创建缓存目录
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        cacheDirectory = documentsPath.appendingPathComponent("ChartCache")
        
        // 确保缓存目录存在
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        
        // 配置编码器
        encoder.dateEncodingStrategy = .iso8601
        decoder.dateDecodingStrategy = .iso8601
        
        Log.info("📊 ChartCacheManager 初始化完成，缓存目录: \(cacheDirectory.path)")
    }
    
    // MARK: - Public Methods
    
    /// 获取指定猫咪和日期的数据
    func getDailyData(for catId: String, date: Date) -> DailyToiletData? {
        let dateString = formatDate(date)
        
        // 先检查内存缓存
        if let cachedData = dailyDataCache[catId]?[dateString] {
            cacheHits += 1
            Log.debug("📊 缓存命中: \(catId) - \(dateString)")
            return cachedData
        }
        
        // 从磁盘加载
        if let diskData = loadDailyDataFromDisk(catId: catId, date: dateString) {
            // 更新内存缓存
            if dailyDataCache[catId] == nil {
                dailyDataCache[catId] = [:]
            }
            dailyDataCache[catId]?[dateString] = diskData
            
            cacheHits += 1
            Log.debug("📊 磁盘缓存命中: \(catId) - \(dateString)")
            return diskData
        }
        
        cacheMisses += 1
        Log.debug("📊 缓存未命中: \(catId) - \(dateString)")
        return nil
    }
    
    /// 保存每日数据
    func saveDailyData(_ data: DailyToiletData) {
        let catId = data.catId
        let dateString = data.date
        
        // 更新内存缓存
        if dailyDataCache[catId] == nil {
            dailyDataCache[catId] = [:]
        }
        dailyDataCache[catId]?[dateString] = data
        
        // 保存到磁盘
        saveDailyDataToDisk(data)
        
        // 更新缓存状态
        updateCacheStatus(for: catId, date: data.dateObject ?? Date())
        
        Log.debug("📊 保存每日数据: \(catId) - \(dateString), 次数: \(data.totalCount)")
    }
    
    /// 批量保存每日数据
    func saveDailyDataBatch(_ dataList: [DailyToiletData]) {
        for data in dataList {
            saveDailyData(data)
        }
        
        // 更新统计信息
        updateStatistics()
        
        Log.info("📊 批量保存完成: \(dataList.count) 条记录")
    }
    
    /// 获取指定猫咪的缓存状态
    func getCacheStatus(for catId: String) -> CacheStatus {
        if let status = cacheStatusMap[catId] {
            return status
        }
        
        // 从磁盘加载缓存状态
        if let diskStatus = loadCacheStatusFromDisk(catId: catId) {
            cacheStatusMap[catId] = diskStatus
            return diskStatus
        }
        
        // 创建新的缓存状态
        let newStatus = CacheStatus(catId: catId)
        cacheStatusMap[catId] = newStatus
        saveCacheStatusToDisk(newStatus)
        return newStatus
    }
    
    /// 获取指定时间范围内缺失的日期
    func getMissingDates(for catId: String, in dateRange: DateRange) -> [Date] {
        let status = getCacheStatus(for: catId)
        let missingDates = status.getMissingDates(in: dateRange)
        
        // 过滤掉今天之后的日期
        let today = Calendar.current.startOfDay(for: Date())
        return missingDates.filter { $0 <= today }
    }
    
    /// 获取指定时间范围内的所有数据
    func getDailyDataRange(for catId: String, in dateRange: DateRange) -> [DailyToiletData] {
        var result: [DailyToiletData] = []
        let calendar = Calendar.current
        var currentDate = calendar.startOfDay(for: dateRange.startDate)
        let endDate = calendar.startOfDay(for: dateRange.endDate)
        
        while currentDate <= endDate {
            if let data = getDailyData(for: catId, date: currentDate) {
                result.append(data)
            }
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        return result.sorted { $0.date < $1.date }
    }
    
    /// 检查数据是否需要更新
    func needsUpdate(for catId: String, date: Date) -> Bool {
        guard let data = getDailyData(for: catId, date: date) else {
            return true // 没有数据，需要获取
        }
        
        return data.needsUpdate
    }
    
    /// 清理过期数据
    func cleanupExpiredData() {
        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .day, value: -ChartCacheConfiguration.maxCacheDays, to: Date()) ?? Date()
        let cutoffString = formatDate(cutoffDate)
        
        var cleanedCount = 0
        
        // 清理内存缓存
        for (catId, catData) in dailyDataCache {
            let filteredData = catData.filter { $0.key >= cutoffString }
            dailyDataCache[catId] = filteredData
            cleanedCount += catData.count - filteredData.count
        }
        
        // 清理磁盘缓存
        cleanupDiskCache(before: cutoffDate)
        
        // 更新统计信息
        updateStatistics()
        
        Log.info("📊 清理过期数据完成，删除 \(cleanedCount) 条记录")
    }
    
    /// 获取缓存统计信息
    func updateStatistics() {
        var totalDays = 0
        let totalCats = dailyDataCache.keys.count
        var oldestDate: Date?
        var newestDate: Date?
        
        for (_, catData) in dailyDataCache {
            totalDays += catData.count
            
            for (dateString, _) in catData {
                if let date = parseDate(dateString) {
                    if oldestDate == nil || date < oldestDate! {
                        oldestDate = date
                    }
                    if newestDate == nil || date > newestDate! {
                        newestDate = date
                    }
                }
            }
        }
        
        let hitRate = cacheHits + cacheMisses > 0 ? Double(cacheHits) / Double(cacheHits + cacheMisses) : 0.0
        let cacheSize = calculateCacheSize()
        
        statistics = CacheStatistics(
            totalCachedDays: totalDays,
            totalCachedCats: totalCats,
            cacheSize: cacheSize,
            oldestCachedDate: oldestDate,
            newestCachedDate: newestDate,
            hitRate: hitRate
        )
    }
    
    // MARK: - Private Methods
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }
    
    private func parseDate(_ dateString: String) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter.date(from: dateString)
    }
    
    private func getDailyDataFileURL(catId: String, date: String) -> URL {
        return cacheDirectory
            .appendingPathComponent(catId)
            .appendingPathComponent("\(date).json")
    }
    
    private func getCacheStatusFileURL(catId: String) -> URL {
        return cacheDirectory
            .appendingPathComponent(catId)
            .appendingPathComponent("cache_status.json")
    }
    
    private func loadDailyDataFromDisk(catId: String, date: String) -> DailyToiletData? {
        let fileURL = getDailyDataFileURL(catId: catId, date: date)
        
        guard let data = try? Data(contentsOf: fileURL),
              let dailyData = try? decoder.decode(DailyToiletData.self, from: data) else {
            return nil
        }
        
        return dailyData
    }
    
    private func saveDailyDataToDisk(_ data: DailyToiletData) {
        let fileURL = getDailyDataFileURL(catId: data.catId, date: data.date)
        
        // 确保目录存在
        try? fileManager.createDirectory(at: fileURL.deletingLastPathComponent(), withIntermediateDirectories: true)
        
        do {
            let jsonData = try encoder.encode(data)
            try jsonData.write(to: fileURL)
        } catch {
            Log.error("📊 保存每日数据失败: \(error.localizedDescription)")
        }
    }
    
    private func loadCacheStatusFromDisk(catId: String) -> CacheStatus? {
        let fileURL = getCacheStatusFileURL(catId: catId)
        
        guard let data = try? Data(contentsOf: fileURL),
              let status = try? decoder.decode(CacheStatus.self, from: data) else {
            return nil
        }
        
        return status
    }
    
    private func saveCacheStatusToDisk(_ status: CacheStatus) {
        let fileURL = getCacheStatusFileURL(catId: status.catId)
        
        // 确保目录存在
        try? fileManager.createDirectory(at: fileURL.deletingLastPathComponent(), withIntermediateDirectories: true)
        
        do {
            let jsonData = try encoder.encode(status)
            try jsonData.write(to: fileURL)
        } catch {
            Log.error("📊 保存缓存状态失败: \(error.localizedDescription)")
        }
    }
    
    private func updateCacheStatus(for catId: String, date: Date) {
        var status = getCacheStatus(for: catId)
        status.addCachedDate(date)
        status.lastFullUpdate = Date()
        
        cacheStatusMap[catId] = status
        saveCacheStatusToDisk(status)
    }
    
    private func cleanupDiskCache(before cutoffDate: Date) {
        let cutoffString = formatDate(cutoffDate)
        
        do {
            let catDirectories = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            
            for catDir in catDirectories {
                guard catDir.hasDirectoryPath else { continue }
                
                let files = try fileManager.contentsOfDirectory(at: catDir, includingPropertiesForKeys: nil)
                
                for file in files {
                    let fileName = file.deletingPathExtension().lastPathComponent
                    if fileName < cutoffString && fileName != "cache_status" {
                        try fileManager.removeItem(at: file)
                    }
                }
            }
        } catch {
            Log.error("📊 清理磁盘缓存失败: \(error.localizedDescription)")
        }
    }
    
    private func calculateCacheSize() -> Int64 {
        var totalSize: Int64 = 0
        
        do {
            let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            
            while let fileURL = enumerator?.nextObject() as? URL {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            }
        } catch {
            Log.error("📊 计算缓存大小失败: \(error.localizedDescription)")
        }
        
        return totalSize
    }
}
