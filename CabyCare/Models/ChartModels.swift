import Foundation
import SwiftUI

// MARK: - Chart Data Type
enum ChartDataType: String, CaseIterable {
    case toilet = "toilet"
    case weight = "weight"

    var displayName: String {
        switch self {
        case .toilet:
            return NSLocalizedString("chart_data_toilet", comment: "如厕次数")
        case .weight:
            return NSLocalizedString("chart_data_weight", comment: "重量")
        }
    }

    var icon: String {
        switch self {
        case .toilet:
            return "chart.bar.fill"
        case .weight:
            return "scalemass.fill"
        }
    }

    var unit: String {
        switch self {
        case .toilet:
            return NSLocalizedString("chart_unit_times", comment: "次")
        case .weight:
            return NSLocalizedString("chart_unit_kg", comment: "kg")
        }
    }
}

// MARK: - Chart Time Period
enum ChartTimePeriod: String, CaseIterable {
    case day = "day"
    case week = "week"
    case month = "month"
    
    var displayName: String {
        switch self {
        case .day:
            return NSLocalizedString("chart_period_day", comment: "Day")
        case .week:
            return NSLocalizedString("chart_period_week", comment: "Week")
        case .month:
            return NSLocalizedString("chart_period_month", comment: "Month")
        }
    }
    
    var icon: String {
        switch self {
        case .day:
            return "calendar"
        case .week:
            return "calendar.badge.clock"
        case .month:
            return "calendar.badge.plus"
        }
    }
    
    // 默认显示的数据点数量
    var defaultDataPoints: Int {
        switch self {
        case .day:
            return 7  // 显示7天
        case .week:
            return 4  // 显示4周
        case .month:
            return 6  // 显示6个月
        }
    }
    
    // 获取时间间隔
    var timeInterval: TimeInterval {
        switch self {
        case .day:
            return 24 * 60 * 60  // 1天
        case .week:
            return 7 * 24 * 60 * 60  // 1周
        case .month:
            return 30 * 24 * 60 * 60  // 1个月（近似）
        }
    }
}

// MARK: - Chart Data Point
struct ToiletChartDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let count: Int
    let duration: TimeInterval // 总时长（秒）

    // 格式化的日期显示
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }

    // 格式化的时长显示（分钟）
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        return "\(minutes)分钟"
    }
}

// MARK: - Weight Chart Data Point
struct WeightChartDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let averageWeight: Double // 平均重量（kg）
    let minWeight: Double? // 最小重量（kg）
    let maxWeight: Double? // 最大重量（kg）
    let recordCount: Int // 该时间段内的记录数量

    // 格式化的日期显示
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }

    // 格式化的重量显示
    var formattedWeight: String {
        return String(format: "%.1fkg", averageWeight)
    }

    // 格式化的重量范围显示
    var formattedWeightRange: String? {
        guard let min = minWeight, let max = maxWeight, recordCount > 1 else {
            return nil
        }
        return String(format: "%.1f - %.1fkg", min, max)
    }
}

// MARK: - Cat Chart Data
struct CatChartData: Identifiable {
    let id = UUID()
    let catId: String
    let catName: String
    let avatarUrl: String?
    let dataType: ChartDataType
    let toiletDataPoints: [ToiletChartDataPoint]
    let weightDataPoints: [WeightChartDataPoint]
    let timePeriod: ChartTimePeriod
    let dateRange: DateRange

    // 便利初始化器 - 如厕数据
    init(catId: String, catName: String, avatarUrl: String?,
         dataPoints: [ToiletChartDataPoint], timePeriod: ChartTimePeriod, dateRange: DateRange) {
        self.catId = catId
        self.catName = catName
        self.avatarUrl = avatarUrl
        self.dataType = .toilet
        self.toiletDataPoints = dataPoints
        self.weightDataPoints = []
        self.timePeriod = timePeriod
        self.dateRange = dateRange
    }

    // 便利初始化器 - 重量数据
    init(catId: String, catName: String, avatarUrl: String?,
         weightDataPoints: [WeightChartDataPoint], timePeriod: ChartTimePeriod, dateRange: DateRange) {
        self.catId = catId
        self.catName = catName
        self.avatarUrl = avatarUrl
        self.dataType = .weight
        self.toiletDataPoints = []
        self.weightDataPoints = weightDataPoints
        self.timePeriod = timePeriod
        self.dateRange = dateRange
    }

    // 完整初始化器
    init(catId: String, catName: String, avatarUrl: String?, dataType: ChartDataType,
         toiletDataPoints: [ToiletChartDataPoint], weightDataPoints: [WeightChartDataPoint],
         timePeriod: ChartTimePeriod, dateRange: DateRange) {
        self.catId = catId
        self.catName = catName
        self.avatarUrl = avatarUrl
        self.dataType = dataType
        self.toiletDataPoints = toiletDataPoints
        self.weightDataPoints = weightDataPoints
        self.timePeriod = timePeriod
        self.dateRange = dateRange
    }

    // 当前数据点（根据数据类型）
    var currentDataPoints: Int {
        switch dataType {
        case .toilet:
            return toiletDataPoints.count
        case .weight:
            return weightDataPoints.count
        }
    }

    // 如厕统计信息
    var totalCount: Int {
        toiletDataPoints.reduce(0) { $0 + $1.count }
    }

    var averageCount: Double {
        guard !toiletDataPoints.isEmpty else { return 0 }
        return Double(totalCount) / Double(toiletDataPoints.count)
    }

    var maxCount: Int {
        toiletDataPoints.map { $0.count }.max() ?? 0
    }

    var minCount: Int {
        toiletDataPoints.map { $0.count }.min() ?? 0
    }

    // 重量统计信息
    var averageWeight: Double {
        guard !weightDataPoints.isEmpty else { return 0 }
        let totalWeight = weightDataPoints.reduce(0.0) { $0 + $1.averageWeight }
        return totalWeight / Double(weightDataPoints.count)
    }

    var maxWeight: Double {
        weightDataPoints.map { $0.averageWeight }.max() ?? 0
    }

    var minWeight: Double {
        weightDataPoints.map { $0.averageWeight }.min() ?? 0
    }

    // 获取趋势（根据数据类型）
    var trend: ChartTrend {
        switch dataType {
        case .toilet:
            return getToiletTrend()
        case .weight:
            return getWeightTrend()
        }
    }

    private func getToiletTrend() -> ChartTrend {
        guard toiletDataPoints.count >= 2 else { return .stable }

        let firstHalf = toiletDataPoints.prefix(toiletDataPoints.count / 2)
        let secondHalf = toiletDataPoints.suffix(toiletDataPoints.count / 2)

        let firstAverage = firstHalf.reduce(0) { $0 + $1.count } / firstHalf.count
        let secondAverage = secondHalf.reduce(0) { $0 + $1.count } / secondHalf.count

        let difference = secondAverage - firstAverage

        if difference > 0 {
            return .increasing
        } else if difference < 0 {
            return .decreasing
        } else {
            return .stable
        }
    }

    private func getWeightTrend() -> ChartTrend {
        guard weightDataPoints.count >= 2 else { return .stable }

        let firstHalf = weightDataPoints.prefix(weightDataPoints.count / 2)
        let secondHalf = weightDataPoints.suffix(weightDataPoints.count / 2)

        let firstAverage = firstHalf.reduce(0.0) { $0 + $1.averageWeight } / Double(firstHalf.count)
        let secondAverage = secondHalf.reduce(0.0) { $0 + $1.averageWeight } / Double(secondHalf.count)

        let difference = secondAverage - firstAverage
        let threshold = 0.1 // 0.1kg的变化阈值

        if difference > threshold {
            return .increasing
        } else if difference < -threshold {
            return .decreasing
        } else {
            return .stable
        }
    }
}

// MARK: - Chart Trend
enum ChartTrend {
    case increasing
    case decreasing
    case stable
    
    var color: Color {
        switch self {
        case .increasing:
            return .green
        case .decreasing:
            return .orange
        case .stable:
            return .blue
        }
    }
    
    var icon: String {
        switch self {
        case .increasing:
            return "arrow.up.right"
        case .decreasing:
            return "arrow.down.right"
        case .stable:
            return "arrow.right"
        }
    }
    
    var description: String {
        switch self {
        case .increasing:
            return NSLocalizedString("chart_trend_increasing", comment: "Increasing")
        case .decreasing:
            return NSLocalizedString("chart_trend_decreasing", comment: "Decreasing")
        case .stable:
            return NSLocalizedString("chart_trend_stable", comment: "Stable")
        }
    }
}

// MARK: - Date Range
struct DateRange: Equatable {
    let startDate: Date
    let endDate: Date
    
    var duration: TimeInterval {
        endDate.timeIntervalSince(startDate)
    }
    
    var formattedRange: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
    }
    
    // 创建默认的日期范围
    static func defaultRange(for period: ChartTimePeriod) -> DateRange {
        let endDate = Date()
        let startDate = Calendar.current.date(
            byAdding: .day,
            value: -period.defaultDataPoints,
            to: endDate
        ) ?? endDate
        
        return DateRange(startDate: startDate, endDate: endDate)
    }
    
    // 创建自定义日期范围
    static func customRange(startDate: Date, endDate: Date) -> DateRange {
        return DateRange(startDate: startDate, endDate: endDate)
    }
}

// MARK: - Chart Configuration
struct ChartConfiguration {
    let showDataPoints: Bool
    let showGrid: Bool
    let animationEnabled: Bool
    let primaryColor: Color
    let secondaryColor: Color
    let backgroundColor: Color
    
    static let `default` = ChartConfiguration(
        showDataPoints: true,
        showGrid: true,
        animationEnabled: true,
        primaryColor: .blue,
        secondaryColor: .gray,
        backgroundColor: .clear
    )
    
    static func themed(for colorScheme: ColorScheme) -> ChartConfiguration {
        return ChartConfiguration(
            showDataPoints: true,
            showGrid: true,
            animationEnabled: true,
            primaryColor: colorScheme == .dark ? .themeSecondary : .themePrimary,
            secondaryColor: colorScheme == .dark ? Color.gray.opacity(0.8) : Color.gray.opacity(0.6),
            backgroundColor: .clear
        )
    }
}

// MARK: - Chart View State
class ChartViewState: ObservableObject {
    @Published var selectedPeriod: ChartTimePeriod = .day
    @Published var selectedDataType: ChartDataType = .toilet
    @Published var isLoading: Bool = false
    @Published var error: String?

    var currentDateRange: DateRange {
        return DateRange.defaultRange(for: selectedPeriod)
    }

    func updatePeriod(_ period: ChartTimePeriod) {
        selectedPeriod = period
    }

    func updateDataType(_ dataType: ChartDataType) {
        selectedDataType = dataType
    }

    func resetToDefault() {
        selectedPeriod = .day
        selectedDataType = .toilet
        error = nil
    }
}
