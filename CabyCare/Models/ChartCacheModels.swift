import Foundation

// MARK: - Daily Data Models

/// 按天存储的猫咪如厕数据
struct DailyToiletData: Codable, Equatable {
    let date: String // YYYY-MM-DD格式
    let catId: String
    let segments: [VideoSegment]
    let totalCount: Int
    let totalDuration: TimeInterval
    let lastUpdated: Date

    // 实现Equatable协议
    static func == (lhs: DailyToiletData, rhs: DailyToiletData) -> Bool {
        return lhs.date == rhs.date &&
               lhs.catId == rhs.catId &&
               lhs.totalCount == rhs.totalCount &&
               lhs.totalDuration == rhs.totalDuration
    }
    
    init(date: Date, catId: String, segments: [VideoSegment]) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current

        self.date = formatter.string(from: date)
        self.catId = catId
        self.segments = segments
        self.totalCount = segments.count
        self.totalDuration = segments.reduce(0) { $0 + $1.duration }
        self.lastUpdated = Date()

        // 统计重量数据
        let segmentsWithWeight = segments.filter { $0.weight_cat > 0 }
        let validWeightSegments = segments.filter { $0.weight_cat > 0.1 && $0.weight_cat < 50.0 }

        Log.info("📊 DailyToiletData初始化: 日期=\(self.date), 猫咪=\(catId), 总段数=\(segments.count), 有重量数据=\(segmentsWithWeight.count), 有效重量数据=\(validWeightSegments.count)")

        if !validWeightSegments.isEmpty {
            let weights = validWeightSegments.map { $0.weight_cat }
            let avgWeight = weights.reduce(0.0, +) / Double(weights.count)
            Log.info("📊   重量统计: 平均=\(String(format: "%.1f", avgWeight))kg, 范围=\(String(format: "%.1f", weights.min() ?? 0))-\(String(format: "%.1f", weights.max() ?? 0))kg")
        }
    }
    
    /// 检查数据是否为今天的数据（需要定期更新）
    var isToday: Bool {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return date == formatter.string(from: Date())
    }
    
    /// 检查数据是否需要更新（今天的数据每小时更新一次，历史数据不更新）
    var needsUpdate: Bool {
        if isToday {
            return Date().timeIntervalSince(lastUpdated) > 3600 // 1小时
        }
        return false // 历史数据不需要更新
    }
    
    /// 获取Date对象
    var dateObject: Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter.date(from: date)
    }
}

/// 缓存状态记录
struct CacheStatus: Codable {
    let catId: String
    var cachedDates: Set<String> // YYYY-MM-DD格式的日期集合
    var lastFullUpdate: Date? // 最后一次完整更新时间
    var dataRange: DateRange? // 已缓存的数据范围
    
    init(catId: String) {
        self.catId = catId
        self.cachedDates = []
        self.lastFullUpdate = nil
        self.dataRange = nil
    }
    
    /// 添加已缓存的日期
    mutating func addCachedDate(_ date: Date) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        cachedDates.insert(formatter.string(from: date))
        
        // 更新数据范围
        updateDataRange(with: date)
    }
    
    /// 检查指定日期是否已缓存
    func hasCachedDate(_ date: Date) -> Bool {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return cachedDates.contains(formatter.string(from: date))
    }
    
    /// 获取缺失的日期列表
    func getMissingDates(in dateRange: DateRange) -> [Date] {
        var missingDates: [Date] = []
        let calendar = Calendar.current
        var currentDate = calendar.startOfDay(for: dateRange.startDate)
        let endDate = calendar.startOfDay(for: dateRange.endDate)
        
        while currentDate <= endDate {
            if !hasCachedDate(currentDate) {
                missingDates.append(currentDate)
            }
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        return missingDates
    }
    
    /// 更新数据范围
    private mutating func updateDataRange(with date: Date) {
        if let existingRange = dataRange {
            let newStartDate = min(existingRange.startDate, date)
            let newEndDate = max(existingRange.endDate, date)
            dataRange = DateRange(startDate: newStartDate, endDate: newEndDate)
        } else {
            dataRange = DateRange(startDate: date, endDate: date)
        }
    }
}

/// 聚合的周数据
struct WeeklyToiletData: Equatable {
    let weekStartDate: Date
    let catId: String
    let dailyData: [DailyToiletData]
    let totalCount: Int
    let totalDuration: TimeInterval
    let averageCount: Double
    
    init(weekStartDate: Date, catId: String, dailyData: [DailyToiletData]) {
        self.weekStartDate = weekStartDate
        self.catId = catId
        self.dailyData = dailyData.sorted { $0.date < $1.date }
        self.totalCount = dailyData.reduce(0) { $0 + $1.totalCount }
        self.totalDuration = dailyData.reduce(0) { $0 + $1.totalDuration }
        self.averageCount = dailyData.isEmpty ? 0 : Double(totalCount) / Double(dailyData.count)
    }
}

/// 聚合的月数据
struct MonthlyToiletData: Equatable {
    let monthStartDate: Date
    let catId: String
    let dailyData: [DailyToiletData]
    let totalCount: Int
    let totalDuration: TimeInterval
    let averageCount: Double
    
    init(monthStartDate: Date, catId: String, dailyData: [DailyToiletData]) {
        self.monthStartDate = monthStartDate
        self.catId = catId
        self.dailyData = dailyData.sorted { $0.date < $1.date }
        self.totalCount = dailyData.reduce(0) { $0 + $1.totalCount }
        self.totalDuration = dailyData.reduce(0) { $0 + $1.totalDuration }
        self.averageCount = dailyData.isEmpty ? 0 : Double(totalCount) / Double(dailyData.count)
    }
}

// MARK: - Cache Configuration

/// 缓存配置
struct ChartCacheConfiguration {
    /// 最大缓存天数（默认保留1年的数据）
    static let maxCacheDays: Int = 365
    
    /// 今天数据的更新间隔（1小时）
    static let todayUpdateInterval: TimeInterval = 3600
    
    /// 批量获取数据的最大天数（一次API调用最多获取30天）
    static let maxBatchDays: Int = 30
    
    /// 预加载的历史数据天数
    static let preloadHistoryDays: Int = 30
    
    /// 缓存文件名前缀
    static let cacheFilePrefix = "chart_cache"
    
    /// 获取指定时间段需要预加载的天数
    static func getPreloadDays(for period: ChartTimePeriod) -> Int {
        switch period {
        case .day:
            return 7 // 7天
        case .week:
            return 8 * 7 + 7 // 8周 + 额外7天确保完整周数据 = 63天
        case .month:
            return 8 * 31 // 8个月，按31天计算 = 248天
        }
    }
    
    /// 获取数据范围
    static func getDataRange(for period: ChartTimePeriod, from endDate: Date = Date()) -> DateRange {
        let calendar = Calendar.current
        let days = getPreloadDays(for: period)
        let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) ?? endDate
        return DateRange(startDate: startDate, endDate: endDate)
    }
}

// MARK: - Cache Statistics

/// 缓存统计信息
struct CacheStatistics {
    let totalCachedDays: Int
    let totalCachedCats: Int
    let cacheSize: Int64 // 字节
    let oldestCachedDate: Date?
    let newestCachedDate: Date?
    let hitRate: Double // 缓存命中率
    
    static let empty = CacheStatistics(
        totalCachedDays: 0,
        totalCachedCats: 0,
        cacheSize: 0,
        oldestCachedDate: nil,
        newestCachedDate: nil,
        hitRate: 0.0
    )
}

// MARK: - Helper Extensions

extension DateRange: Codable {
    enum CodingKeys: String, CodingKey {
        case startDate, endDate
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        startDate = try container.decode(Date.self, forKey: .startDate)
        endDate = try container.decode(Date.self, forKey: .endDate)
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(startDate, forKey: .startDate)
        try container.encode(endDate, forKey: .endDate)
    }
}

extension Calendar {
    /// 获取指定日期所在周的开始日期（周一）
    func startOfWeek(for date: Date) -> Date {
        // 设置一周的第一天为周一
        var calendar = self
        calendar.firstWeekday = 2 // 2 = 周一

        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: date)
        return calendar.date(from: components) ?? date
    }

    /// 获取指定日期所在周的结束日期（周日）
    func endOfWeek(for date: Date) -> Date {
        let startOfWeek = self.startOfWeek(for: date)
        return self.date(byAdding: .day, value: 6, to: startOfWeek) ?? date
    }

    /// 获取指定日期所在月的开始日期（如果不存在则创建）
    func monthStart(for date: Date) -> Date {
        let components = dateComponents([.year, .month], from: date)
        return self.date(from: components) ?? date
    }

    /// 获取指定日期所在月的结束日期
    func endOfMonth(for date: Date) -> Date {
        let startOfMonth = monthStart(for: date)
        let nextMonth = self.date(byAdding: .month, value: 1, to: startOfMonth) ?? date
        return self.date(byAdding: .day, value: -1, to: nextMonth) ?? date
    }
}

extension Date {
    /// 格式化为字符串（用于日志和调试）
    func formattedString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: self)
    }
}
