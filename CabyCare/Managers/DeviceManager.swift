import Foundation

/// 设备管理器类 - 负责管理设备及其相关的视频段
class DeviceManager: ObservableObject {
    /// 当前用户的设备
    @Published private(set) var devices: [Device] = []

    /// 当前选中的设备
    @Published private(set) var selectedDevice: Device?

    /// 设备的视频片段
    private var deviceSegments: [String: [VideoSegment]] = [:]

    /// 设备的日期缓存
    private var deviceDates: [String: [Date]] = [:]

    /// 缓存相关属性
    private var lastFullLoadTime: [String: Date] = [:]
    private let cacheExpirationInterval: TimeInterval = 300 // 5分钟缓存过期

    /// 已请求的日期范围缓存，避免重复请求历史数据
    private var cachedDateRanges: [String: [ClosedRange<Date>]] = [:]

    /// 初始化方法 - 设置通知监听
    init() {
        // 监听设备名称更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleDeviceNameUpdated(_:)),
            name: .deviceNameUpdated,
            object: nil
        )
    }

    /// 析构方法 - 移除通知监听
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    /// 处理设备名称更新通知
    @objc private func handleDeviceNameUpdated(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let deviceId = userInfo["deviceId"] as? String,
              let newName = userInfo["newName"] as? String else {
            Log.warning("⚠️ DeviceManager: 设备名称更新通知缺少必要参数")
            return
        }

        Log.info("📱 DeviceManager: 收到设备名称更新通知: \(deviceId) -> \(newName)")

        // 如果已经在主线程，直接执行；否则切换到主线程
        if Thread.isMainThread {
            performDeviceNameUpdateSync(deviceId: deviceId, newName: newName)
        } else {
            DispatchQueue.main.sync { [weak self] in
                self?.performDeviceNameUpdateSync(deviceId: deviceId, newName: newName)
            }
        }
    }

    /// 执行设备名称更新 - 同步版本，必须在主线程调用
    private func performDeviceNameUpdateSync(deviceId: String, newName: String) {
        assert(Thread.isMainThread, "performDeviceNameUpdateSync must be called on main thread")
        guard var segments = deviceSegments[deviceId] else {
            Log.info("📱 DeviceManager: 设备 \(deviceId) 没有缓存的视频片段，无需更新")
            return
        }

        // 更新所有该设备的视频片段中的设备名称
        for i in 0..<segments.count {
            segments[i].updateDeviceName(newName)
        }

        // 保存更新后的片段
        deviceSegments[deviceId] = segments

        Log.info("📱 DeviceManager: 已更新设备 \(deviceId) 的 \(segments.count) 个视频片段的设备名称为: \(newName)")

        // 同时更新devices数组中的设备名称（如果存在）
        if let index = devices.firstIndex(where: { $0.deviceId == deviceId }) {
            let updatedDevice = devices[index]
            // 创建一个新的Device实例，更新name属性
            let newDevice = Device(
                deviceId: updatedDevice.deviceId,
                userId: updatedDevice.userId,
                hardwareSn: updatedDevice.hardwareSn,
                name: newName,
                model: updatedDevice.model,
                timezone: updatedDevice.timezone,
                firmwareVersion: updatedDevice.firmwareVersion,
                status: updatedDevice.status,
                lastHeartbeat: updatedDevice.lastHeartbeat,
                lastActive: updatedDevice.lastActive,
                createdAt: updatedDevice.createdAt,
                updatedAt: updatedDevice.updatedAt
            )
            devices[index] = newDevice
            Log.info("📱 DeviceManager: 已更新本地设备列表中设备 \(deviceId) 的名称为: \(newName)")
        }
    }
    
    /// 获取设备的视频片段
    func getSegments(for deviceId: String) -> [VideoSegment] {
        return deviceSegments[deviceId] ?? []
    }
    
    /// 获取设备的可用日期
    func getAvailableDates(for deviceId: String) -> [Date] {
        return deviceDates[deviceId] ?? []
    }
    
    /// 获取设备的时区
    func getDeviceTimezone(for deviceId: String? = nil) -> String {
        if let deviceId = deviceId, let device = devices.first(where: { $0.deviceId == deviceId }) {
            return device.timezone
        }
        return selectedDevice?.timezone ?? "UTC"
    }
    
    /// 基于设备时区的日历对象
    func deviceCalendar(for deviceId: String? = nil) -> Calendar {
        var calendar = Calendar.current
        calendar.timeZone = TimeZone(identifier: getDeviceTimezone(for: deviceId)) ?? .current
        return calendar
    }
    
    /// 加载用户的设备
    @MainActor
    func loadDevices(for userId: String) async throws -> [Device] {
        guard AuthManager.shared.isAuthenticated,
            AuthManager.shared.accessToken != nil else {
            // Log.warning("⚠️ 尝试获取设备列表但用户未认证")
            throw NetworkError.unauthorized
        }

        let url = URL(string: Configuration.API.Device.getAccessibleDevicesPath(userId: userId))!
        
        do {
            // 使用新的可访问设备 API
            let response: AccessibleDevicesResponse = try await NetworkManager.shared.request(url)
            
            // 处理成功响应
            Log.info("📱 获取可访问设备成功: 状态=\(response.status), 消息=\(response.message), 设备数量=\(response.data.count)")
            
            // 如果没有设备，记录友好的信息
            if response.data.isEmpty {
                Log.info("ℹ️ 用户 \(userId) 目前没有绑定任何设备")
            } else {
                // 详细打印每个设备信息
                for device in response.data {
                    Log.info("📱 设备详情: ID=\(device.deviceId), 名称=\(device.name), 型号=\(device.model), 状态=\(device.status)")
                }
            }
            
            // 将 AccessibleDevice 转换为 Device
            let loadedDevices = response.data.map { accessibleDevice in
                Device(
                    deviceId: accessibleDevice.deviceId,
                    userId: accessibleDevice.userId,
                    hardwareSn: accessibleDevice.hardwareSn,
                    name: accessibleDevice.name,
                    model: accessibleDevice.model,
                    timezone: accessibleDevice.timezone,
                    firmwareVersion: accessibleDevice.firmwareVersion,
                    status: accessibleDevice.status,
                    lastHeartbeat: parseDate(accessibleDevice.lastHeartbeat),
                    lastActive: parseDate(accessibleDevice.lastActive),
                    createdAt: parseDate(accessibleDevice.createdAt) ?? Date(),
                    updatedAt: parseDate(accessibleDevice.updatedAt) ?? Date()
                )
            }
            
            // 更新设备列表
            self.devices = loadedDevices
            
            // 如果没有选择设备，则选择第一个
            if selectedDevice == nil, let firstDevice = loadedDevices.first {
                self.selectedDevice = firstDevice
                Log.info("🎯 自动选择第一个设备: \(firstDevice.name)")
            } else if loadedDevices.isEmpty {
                self.selectedDevice = nil
                Log.info("📱 没有可用设备，清除当前选择")
            }
            
            return loadedDevices
            
        } catch let error as NetworkError {
            switch error {
            case .decodingError(let underlyingError):
                Log.error("❌ 设备列表解码错误: \(underlyingError.localizedDescription)")
                Log.warning("💡 这可能是新用户没有绑定设备导致的，使用空设备列表")
                
                // 对于解码错误，假设是新用户，返回空列表
                self.devices = []
                self.selectedDevice = nil
                return []
                
            case .unauthorized:
                Log.error("🔒 获取设备列表失败: 用户未授权")
                throw error
                
            case .cloudflareTimeout:
                Log.error("☁️ 获取设备列表失败: Cloudflare超时")
                throw error
                
            default:
                Log.error("❌ 获取设备列表失败: \(error.errorDescription ?? error.localizedDescription)")
                throw error
            }
        } catch {
            Log.error("❌ 获取设备列表时发生未知错误: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 选择设备
    @MainActor
    func selectDevice(_ device: Device) {
        self.selectedDevice = device
    }
    
    /// 加载设备的视频段
    @MainActor
    func loadSegments(for device: Device, startDate: Date, endDate: Date) async throws -> [VideoSegment] {
        // 检查是否已有该日期范围的缓存
        let deviceId = device.deviceId
        
        // 检查已缓存的日期范围
        if let existingRanges = cachedDateRanges[deviceId] {
            let isCovered = existingRanges.contains { existingRange in
                existingRange.contains(startDate) && existingRange.contains(endDate)
            }
            
            if isCovered {
                Log.info("📊 DeviceManager: 设备 \(device.name) 的日期范围 \(DateFormatterUtil.format(startDate, as: .api)) 到 \(DateFormatterUtil.format(endDate, as: .api)) 已有缓存，直接返回")
                // 返回缓存中该日期范围的数据
                let calendar = deviceCalendar(for: deviceId)
                return deviceSegments[deviceId]?.filter { segment in
                    segment.start >= calendar.startOfDay(for: startDate) && 
                    segment.start < calendar.date(byAdding: .day, value: 1, to: calendar.startOfDay(for: endDate))!
                } ?? []
            }
        }
        
        // 日期验证：确保不会请求未来的日期
        let now = Date()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: now)
        
        // 如果开始日期是未来日期，调整为今天
        let validatedStartDate: Date
        if startDate > today {
            Log.warning("⚠️ 检测到未来日期请求: \(startDate)，调整为今天: \(today)")
            validatedStartDate = today
        } else {
            validatedStartDate = startDate
        }
        
        // 如果结束日期是未来日期，调整为明天
        let validatedEndDate: Date
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        if endDate > tomorrow {
            Log.warning("⚠️ 检测到未来结束日期: \(endDate)，调整为明天: \(tomorrow)")
            validatedEndDate = tomorrow
        } else {
            validatedEndDate = endDate
        }
        
        let startDateStr = DateFormatterUtil.format(validatedStartDate, as: .api)
        let endDateStr = DateFormatterUtil.format(validatedEndDate, as: .api)
        
        // Log.info("📱 Loading videos for device \(device.name) from \(startDateStr) to \(endDateStr)")

        let url = URL(string: Configuration.API.Video.listPath(
            deviceId: device.deviceId,
            start: startDateStr,
            end: endDateStr
        ))!

        // 添加详细的调试日志
        Log.info("🔗 视频列表请求URL: \(url.absoluteString)")
        Log.info("🆔 设备ID: \(device.deviceId)")
        Log.info("📅 请求日期范围: \(startDateStr) 到 \(endDateStr)")
        
        do {
            let segments: [VideoSegment]
            
            do {
                segments = try await NetworkManager.shared.request(url)
                Log.info("✅ 设备 \(device.name) 在日期范围 \(startDateStr) 到 \(endDateStr) 获得 \(segments.count) 个视频片段")
            } catch NetworkError.noData {
                // 服务器返回null，表示该设备在该日期范围内没有视频数据
                Log.info("ℹ️ 设备 \(device.name) 在日期范围 \(startDateStr) 到 \(endDateStr) 内没有视频数据，这是正常情况")
                segments = []
            }
            
            // 为每个segment添加设备名称
            let segmentsWithDeviceName = segments.map { segment in
                var updatedSegment = segment
                updatedSegment.deviceName = device.name
                return updatedSegment
            }
            
            // 使用设备时区格式化日志输出
            let deviceTZ = TimeZone(identifier: device.timezone) ?? .current
            let dateFormatter = DateFormatter()
            dateFormatter.dateStyle = .medium
            dateFormatter.timeStyle = .medium
            dateFormatter.timeZone = deviceTZ
            
            // if let firstSegment = segmentsWithDeviceName.first {
            //     let localFirstTime = dateFormatter.string(from: firstSegment.start)
            //     Log.info("the first segment start time: \(localFirstTime) [\(device.timezone)]")
            // }
            
            // if let lastSegment = segmentsWithDeviceName.last {
            //     let localLastTime = dateFormatter.string(from: lastSegment.start)
            //     Log.info("the last segment start time: \(localLastTime) [\(device.timezone)]")
            // }
            
            // 计算并存储可用日期
            let calendar = deviceCalendar(for: deviceId)
            
            // 合并到现有segments中
            var allSegments = deviceSegments[deviceId] ?? []
            
            // 移除重复的段（基于ID）
            let existingIds = Set(allSegments.map { $0.id })
            let newSegments = segmentsWithDeviceName.filter { !existingIds.contains($0.id) }
            
            allSegments.append(contentsOf: newSegments)
            
            // 按时间排序
            allSegments.sort { $0.start > $1.start }
            
            deviceSegments[deviceId] = allSegments
            
            // 计算可用日期
            let allDates = Set(allSegments.map { calendar.startOfDay(for: $0.start) })
            deviceDates[deviceId] = Array(allDates).sorted(by: >)
            
            // 记录缓存的日期范围
            let validatedRange = validatedStartDate...validatedEndDate
            if cachedDateRanges[deviceId] == nil {
                cachedDateRanges[deviceId] = [validatedRange]
            } else {
                cachedDateRanges[deviceId]?.append(validatedRange)
                // 优化缓存范围
                optimizeCachedRanges(for: deviceId)
            }
            
            // 更新加载时间
            lastFullLoadTime[deviceId] = Date()
            
            Log.info("📊 DeviceManager: 设备 \(device.name) 缓存更新完成，总片段数: \(allSegments.count)，可用日期数: \(allDates.count)")
            
            return segmentsWithDeviceName
        } catch {
            Log.error("❌ 设备 \(device.name) 加载视频失败: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 获取设备特定日期的视频片段
    @MainActor
    func getDailySegments(for device: Device, date: Date) -> [VideoSegment] {
        let deviceId = device.deviceId
        let calendar = deviceCalendar(for: deviceId)
        
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        return deviceSegments[deviceId]?.filter { segment in
            let segmentDate = segment.start
            return segmentDate >= startOfDay && segmentDate < endOfDay
        } ?? []
    }
    
    /// 检查是否需要刷新缓存
    func needsRefresh(for deviceId: String) -> Bool {
        return deviceSegments[deviceId]?.isEmpty ?? true ||
               (lastFullLoadTime[deviceId].map { Date().timeIntervalSince($0) > cacheExpirationInterval } ?? true)
    }
    
    /// 检查指定日期的数据是否已在缓存中
    func hasDataInCache(for deviceId: String, date: Date) -> Bool {
        guard let ranges = cachedDateRanges[deviceId] else { return false }
        
        let calendar = deviceCalendar(for: deviceId)
        let startOfDay = calendar.startOfDay(for: date)
        
        return ranges.contains { range in
            range.contains(startOfDay)
        }
    }
    
    /// 检查指定日期范围的数据是否已在缓存中
    func hasDataInCache(for deviceId: String, startDate: Date, endDate: Date) -> Bool {
        guard let ranges = cachedDateRanges[deviceId] else { return false }
        
        return ranges.contains { range in
            range.contains(startDate) && range.contains(endDate)
        }
    }
    
    /// 优化缓存：合并重叠的日期范围
    private func optimizeCachedRanges(for deviceId: String) {
        guard var ranges = cachedDateRanges[deviceId], ranges.count > 1 else { return }
        
        // 按开始日期排序
        ranges.sort { $0.lowerBound < $1.lowerBound }
        
        var optimizedRanges: [ClosedRange<Date>] = []
        var currentRange = ranges[0]
        
        for i in 1..<ranges.count {
            let nextRange = ranges[i]
            
            // 检查是否可以合并
            if currentRange.upperBound >= nextRange.lowerBound {
                // 合并范围
                let newEnd = max(currentRange.upperBound, nextRange.upperBound)
                currentRange = currentRange.lowerBound...newEnd
            } else {
                optimizedRanges.append(currentRange)
                currentRange = nextRange
            }
        }
        
        optimizedRanges.append(currentRange)
        cachedDateRanges[deviceId] = optimizedRanges
        
        Log.info("📊 DeviceManager: 设备 \(deviceId) 缓存范围优化完成，从 \(ranges.count) 个范围合并为 \(optimizedRanges.count) 个")
    }
    
    /// 清除缓存
    @MainActor
    func clearCache(for deviceId: String? = nil) {
        if let deviceId = deviceId {
            deviceSegments.removeValue(forKey: deviceId)
            deviceDates.removeValue(forKey: deviceId)
            lastFullLoadTime.removeValue(forKey: deviceId)
            cachedDateRanges.removeValue(forKey: deviceId)
        } else {
            deviceSegments.removeAll()
            deviceDates.removeAll()
            lastFullLoadTime.removeAll()
            cachedDateRanges.removeAll()
        }
    }
    
    /// 更新视频段信息
    @MainActor
    func updateSegment(_ segment: VideoSegment, for deviceId: String) {
        if var segments = deviceSegments[deviceId] {
            if let index = segments.firstIndex(where: { $0.id == segment.id }) {
                var updatedSegment = segment
                // 确保设备名称被保留
                if let device = devices.first(where: { $0.deviceId == deviceId }) {
                    updatedSegment.deviceName = device.name
                }
                segments[index] = updatedSegment
                deviceSegments[deviceId] = segments
            }
        }
    }


    
    /// 解析日期字符串
    private func parseDate(_ dateString: String?) -> Date? {
        guard let dateString = dateString else { return nil }
        return DateFormatterUtil.date(from: dateString, format: .iso8601)
    }
}
