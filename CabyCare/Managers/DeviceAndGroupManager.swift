import Foundation
import SwiftUI
import UIKit
import os

/// 设备和家庭组管理器 - 负责设备和家庭组相关的网络请求和业务逻辑
@MainActor
class DeviceAndGroupManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var devices: [DeviceStatusResponse] = []
    @Published var familyGroups: [FamilyGroup] = []
    @Published var invitations: [InvitationResponse] = []
    @Published var isFetchingDevices = false
    @Published var isFetchingGroups = false
    @Published var isLoadingInvitations = false
    @Published var isAutoRefreshing = false
    
    // MARK: - 传感器状态管理
    @Published private(set) var sensorStatuses: [String: DeviceSensorStatusResponse] = [:]
    @Published private(set) var isFetchingSensorStatus: Set<String> = []
    @Published private(set) var sensorStatusErrors: [String: Error] = [:]
    
    // MARK: - Auto Refresh Properties
    nonisolated(unsafe) private var refreshTimer: DispatchSourceTimer?
    private let refreshInterval: TimeInterval = 15 * 60 // 15分钟
    private let timerQueue = DispatchQueue(label: "com.cabycare.device.refresh", qos: .utility)
    private var isRefreshInProgress = OSAllocatedUnfairLock(initialState: false)
    private var lastRefreshTime: Date?
    private let minRefreshInterval: TimeInterval = 60 // 最小刷新间隔1分钟，防止过度刷新
    
    // MARK: - Lifecycle
    
    init() {
        // 首先加载缓存的设备数据，立即显示给用户
        loadDevicesFromCache()
        setupAutoRefresh()
        setupNotificationObservers()
    }
    
    deinit {
        // 在 deinit 中清理所有资源
        Log.info("🧹 DeviceAndGroupManager deinit - 清理资源")
        
        // 停止自动刷新定时器
        stopAutoRefresh()
        
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
        
        Log.info("✅ DeviceAndGroupManager 资源清理完成")
    }
    
    // MARK: - 设备缓存管理
    
    private let deviceCacheKey = "cached_device_list"
    private let deviceCacheExpiryKey = "cached_device_list_expiry"
    private let cacheValidityPeriod: TimeInterval = 24 * 60 * 60 // 24小时缓存有效期
    
    /// 从缓存加载设备数据，立即显示给用户
    @MainActor
    private func loadDevicesFromCache() {
        guard let cacheData = UserDefaults.standard.data(forKey: deviceCacheKey),
              let cachedDevices = try? JSONDecoder().decode([DeviceStatusResponse].self, from: cacheData) else {
            Log.debug("📱 没有找到设备缓存数据")
            return
        }
        
        // 检查缓存是否过期
        let cacheExpiry = UserDefaults.standard.double(forKey: deviceCacheExpiryKey)
        let now = Date().timeIntervalSince1970
        
        if now - cacheExpiry > cacheValidityPeriod {
            Log.debug("📱 设备缓存已过期，清除缓存")
            UserDefaults.standard.removeObject(forKey: deviceCacheKey)
            UserDefaults.standard.removeObject(forKey: deviceCacheExpiryKey)
            return
        }
        
        // 加载缓存的设备列表
        self.devices = cachedDevices
        Log.info("📱 从缓存加载了 \(cachedDevices.count) 台设备，立即显示给用户")
        
        // 应用排序
        applySortingToDevices()
    }
    
    /// 保存设备数据到缓存
    @MainActor
    private func saveDevicesToCache() {
        guard let cacheData = try? JSONEncoder().encode(devices) else {
            Log.error("❌ 无法编码设备数据用于缓存")
            return
        }
        
        UserDefaults.standard.set(cacheData, forKey: deviceCacheKey)
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: deviceCacheExpiryKey)
        Log.debug("📱 已保存 \(devices.count) 台设备到缓存")
    }
    
    // MARK: - Auto Refresh Setup
    
    nonisolated private func setupAutoRefresh() {
        startAutoRefresh()
    }
    
    private func setupNotificationObservers() {
        // 监听应用生命周期
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
    }
    
    @objc private func appDidBecomeActive() {
        Log.info("📱 应用进入前台，恢复设备状态自动刷新")
        
        // 检查对象是否仍然有效
        guard !isFetchingDevices else {
            Log.debug("📱 应用进入前台时正在获取设备，跳过自动刷新启动")
            return
        }
        
        startAutoRefresh()
        
        // 如果上次刷新时间超过5分钟，立即刷新一次
        if let lastRefresh = lastRefreshTime,
           Date().timeIntervalSince(lastRefresh) > 300 {
            // 安全地在 MainActor 上执行
            Task { @MainActor [weak self] in
                guard let self = self else { 
                    Log.debug("📱 应用前台刷新时 DeviceAndGroupManager 已被释放")
                    return 
                }
                
                do {
                    await self.refreshAllDevicesStatus()
                }
            }
        }
    }
    
    @objc private func appDidEnterBackground() {
        Log.info("📱 应用进入后台，暂停设备状态自动刷新")
        stopAutoRefresh()
    }
    
    nonisolated private func startAutoRefresh() {
        stopAutoRefresh() // 先停止现有的定时器
        
        refreshTimer = DispatchSource.makeTimerSource(queue: timerQueue)
        refreshTimer?.schedule(deadline: .now() + refreshInterval, repeating: refreshInterval)
        refreshTimer?.setEventHandler { [weak self] in
            guard let self = self else { 
                Log.debug("🔄 定时器回调时 DeviceAndGroupManager 已被释放")
                return 
            }
            
            // 安全地在 MainActor 上执行，添加错误处理
            Task { @MainActor [weak self] in
                guard let self = self else { 
                    Log.debug("🔄 执行定时刷新时 DeviceAndGroupManager 已被释放")
                    return 
                }
                
                do {
                    await self.performScheduledRefresh()
                }
            }
        }
        refreshTimer?.resume()
        
        Log.info("⏰ 设备状态自动刷新已启动，间隔：\(Int(refreshInterval/60))分钟")
    }
    
    nonisolated private func stopAutoRefresh() {
        if let timer = refreshTimer {
            timer.cancel()
            refreshTimer = nil
            Log.info("⏰ 设备状态自动刷新已停止")
        } else {
            Log.debug("⏰ 设备状态自动刷新已经处于停止状态")
        }
    }
    
    @MainActor
    private func performScheduledRefresh() async {
        // 检查是否需要刷新（避免在用户主动操作时进行自动刷新）
        guard !isFetchingDevices, !isRefreshInProgress.withLock({ $0 }) else {
            Log.debug("🔄 跳过自动刷新：正在进行其他操作")
            return
        }
        
        // 检查最小刷新间隔
        if let lastRefresh = lastRefreshTime,
           Date().timeIntervalSince(lastRefresh) < minRefreshInterval {
            Log.debug("🔄 跳过自动刷新：距离上次刷新时间过短")
            return
        }
        
        Log.info("🔄 执行定时设备状态刷新")
        isAutoRefreshing = true
        await refreshAllDevicesStatus()
        isAutoRefreshing = false
    }
    
    // MARK: - Device Management
    
    /// 清除所有设备相关的缓存
    @MainActor
    func clearAllDeviceCache() {
        Log.info("🧹 开始清除所有设备相关缓存")
        
        // 1. 清除当前设备列表
        devices = []
        
        // 2. 清除传感器状态缓存
        sensorStatuses.removeAll()
        sensorStatusErrors.removeAll()
        isFetchingSensorStatus.removeAll()
        
        // 3. 清除自动刷新相关状态
        lastRefreshTime = nil
        isRefreshInProgress.withLock { $0 = false }
        
        // 4. 清除UserDefaults中所有可能的设备相关缓存
        let deviceCacheKeys = [
            "DeviceStatusCache",
            "DeviceCacheLastUpdate",
            "cached_devices",
            "device_cache_expiry",
            "last_device_refresh",
            "device_list_cache",
            "sensor_status_cache",
            "video_segments_cache",
            "device_dates_cache",
            "device_status_response_cache",
            "accessible_devices_cache",
            "device_heartbeat_cache",
            "device_network_cache",
            // 新的缓存键
            deviceCacheKey,
            deviceCacheExpiryKey
        ]
        
        for key in deviceCacheKeys {
            UserDefaults.standard.removeObject(forKey: key)
        }
        
        UserDefaults.standard.synchronize()
        
        // 5. 发送设备缓存清除通知，通知其他组件
        NotificationCenter.default.post(name: .deviceCacheCleared, object: nil)
        
        Log.info("✅ 设备缓存清除完成")
    }
    
    /// 获取用户可访问的设备列表 - 分阶段加载
    @MainActor
    func fetchAccessibleDevices(forceRefresh: Bool = false) async {
        guard let userId = UserDefaultsManager.shared.userId else { 
            Log.error("❌ 无法获取用户ID")
            return 
        }
        
        // 防止重复刷新
        guard !isRefreshInProgress.withLock({ $0 }) else {
            Log.debug("📱 设备获取已在进行中，跳过重复请求")
            return
        }
        
        Log.info("📱 开始获取设备列表 (用户ID: \(userId), 强制刷新: \(forceRefresh))")
        
        // 如果是强制刷新，先清除缓存
        if forceRefresh {
            clearAllDeviceCache()
        }
        
        isFetchingDevices = true
        isRefreshInProgress.withLock { $0 = true }
        defer { 
            isFetchingDevices = false
            isRefreshInProgress.withLock { $0 = false }
        }
        
        do {
            // 首先获取设备列表基本信息
            let urlString = Configuration.API.Device.getAccessibleDevicesPath(userId: userId)
            guard let url = URL(string: urlString) else {
                throw NetworkError.invalidURL
            }
            
            let response: AccessibleDevicesResponse = try await NetworkManager.shared.request(url)
            Log.info("📋 获取设备列表成功: 状态=\(response.status), 消息=\(response.message), 设备数量=\(response.data.count)")
            
            // 📊 验证数据新鲜度 - 检查返回的设备数量是否符合预期
            if response.data.isEmpty {
                Log.warning("⚠️ 服务器返回空设备列表，这可能表示：")
                Log.warning("   1. 用户真的没有绑定任何设备")
                Log.warning("   2. 服务器端数据同步延迟")
                Log.warning("   3. 用户权限发生变化")
                
                // 清空设备列表并保存到缓存
                self.devices = []
                saveDevicesToCache()
                return
            }
            
            // 🚀 第一阶段：立即创建设备列表显示基本信息（不等待在线状态）
            var deviceStatusList: [DeviceStatusResponse] = []
            for accessibleDevice in response.data {
                let basicDevice = DeviceStatusResponse(
                    id: accessibleDevice.deviceId,
                    name: accessibleDevice.name,
                    model: accessibleDevice.model,
                    firmware: accessibleDevice.firmwareVersion,
                    online: nil, // 缓存数据，在线状态未知，稍后更新
                    lastHeartbeat: accessibleDevice.lastHeartbeat,
                    ipv4: nil, // 稍后更新
                    ipv6: nil  // 稍后更新
                )
                deviceStatusList.append(basicDevice)
                
                Log.info("📱 基础设备信息（状态未知）: \(basicDevice.name) (ID: \(basicDevice.id))")
            }
            
            // 立即更新设备列表，让用户看到设备列表
            self.devices = deviceStatusList
            applySortingToDevices()
            saveDevicesToCache()
            
            Log.info("🚀 第一阶段完成：已显示 \(deviceStatusList.count) 台设备的基本信息")
            
            // 🔄 第二阶段：异步获取详细状态信息（在线状态、IP信息等）
            Log.info("🔄 第二阶段开始：异步获取设备详细状态")
            await updateDeviceDetailedStatus(deviceIds: deviceStatusList.map { $0.id })
            
            // 🔍 第三阶段：异步获取传感器状态
            Log.info("🔍 第三阶段开始：异步获取传感器状态")
            Task {
                let deviceIds = self.devices.map { $0.id }
                await fetchSensorStatuses(for: deviceIds)
            }
            
            lastRefreshTime = Date()
            
        } catch {
            Log.error("❌ 获取可访问设备失败: \(error.localizedDescription)")
            
            // 如果是强制刷新失败，确保清空现有数据
            if forceRefresh {
                Log.warning("🔄 强制刷新失败，清空现有设备数据")
                devices = []
                saveDevicesToCache()
            }
        }
    }
    
    /// 第二阶段：异步更新设备详细状态信息
    @MainActor
    private func updateDeviceDetailedStatus(deviceIds: [String]) async {
        Log.info("🔄 开始更新 \(deviceIds.count) 台设备的详细状态")
        
        var updatedDevices = devices
        var updateCount = 0
        
        // 并发获取所有设备的详细状态
        await withTaskGroup(of: (String, DeviceStatusResponse?).self) { group in
            for deviceId in deviceIds {
                group.addTask {
                    let status = await self.fetchDeviceStatus(deviceId: deviceId)
                    return (deviceId, status)
                }
            }
            
            for await (deviceId, detailedStatus) in group {
                if let detailedStatus = detailedStatus,
                   let index = updatedDevices.firstIndex(where: { $0.id == deviceId }) {
                    
                    // 更新设备详细信息，保留基础信息
                    let updatedDevice = DeviceStatusResponse(
                        id: deviceId,
                        name: detailedStatus.name,
                        model: detailedStatus.model,
                        firmware: detailedStatus.firmware,
                        online: detailedStatus.online, // 更新真实的在线状态
                        lastHeartbeat: detailedStatus.lastHeartbeat,
                        ipv4: detailedStatus.ipv4, // 更新IP信息
                        ipv6: detailedStatus.ipv6
                    )
                    
                    updatedDevices[index] = updatedDevice
                    updateCount += 1
                    
                    // 实时更新UI（每获取到一个设备状态就更新一次）
                    self.devices = updatedDevices
                    applySortingToDevices()
                    
                    Log.debug("✅ 更新设备详细状态: \(updatedDevice.name) - \(updatedDevice.online == true ? "在线" : updatedDevice.online == false ? "离线" : "状态未知")")
                }
            }
        }
        
        // 保存最终的设备状态到缓存
        saveDevicesToCache()
        
        Log.info("🔄 第二阶段完成：已更新 \(updateCount)/\(deviceIds.count) 台设备的详细状态")
    }
    
    // MARK: - 设备排序相关
    
    /// 对设备列表进行排序 - 在线设备优先显示
    private func sortDevices(_ devices: [DeviceStatusResponse]) -> [DeviceStatusResponse] {
        return devices.sorted { device1, device2 in
            // 定义优先级：在线(2) > 未知(1) > 离线(0)
            let priority1 = device1.online == true ? 2 : device1.online == false ? 0 : 1
            let priority2 = device2.online == true ? 2 : device2.online == false ? 0 : 1
            
            // 首先按优先级排序
            if priority1 != priority2 {
                return priority1 > priority2
            }
            
            // 如果优先级相同，按设备名称排序
            return device1.name.localizedCompare(device2.name) == .orderedAscending
        }
    }
    
    /// 应用排序到设备列表
    private func applySortingToDevices() {
        devices = sortDevices(devices)
    }
    
    // MARK: - 传感器状态管理
    
    /// 获取指定设备的传感器状态
    func getSensorStatus(for deviceId: String) -> DeviceSensorStatusResponse? {
        return sensorStatuses[deviceId]
    }
    
    /// 获取单个设备的传感器状态
    @MainActor
    func fetchSensorStatus(for deviceId: String) async {
        guard !isFetchingSensorStatus.contains(deviceId) else { 
            Log.debug("🔍 设备 \(deviceId) 传感器状态正在获取中，跳过重复请求")
            return 
        }
        
        guard let userId = UserDefaultsManager.shared.userId else {
            Log.error("❌ 无法获取用户ID")
            return
        }
        
        Log.debug("🔍 开始获取设备 \(deviceId) 的传感器状态")
        
        isFetchingSensorStatus.insert(deviceId)
        sensorStatusErrors.removeValue(forKey: deviceId)
        
        do {
            // 构建API URL - 基于 test_device_status.sh 中的API
            let urlString = "\(Configuration.API.cabyBaseUrl)/api/devices/\(deviceId)/sensor-status?user_id=\(userId)"
            guard let url = URL(string: urlString) else {
                throw SensorStatusError(message: "无效的API URL")
            }
            
            // 使用NetworkManager统一处理请求
            let sensorStatus: DeviceSensorStatusResponse = try await NetworkManager.shared.request(url, method: .get, contentType: .json)
            
            // 更新传感器状态
            sensorStatuses[deviceId] = sensorStatus
            
            let errorCount = sensorStatus.errorCount
            if errorCount > 0 {
                Log.warning("⚠️ 设备 \(deviceId) 有 \(errorCount) 个传感器错误")
            } else {
                Log.debug("✅ 设备 \(deviceId) 所有传感器状态正常")
            }
            
        } catch let error as NetworkError {
            // 处理不同类型的网络错误
            switch error {
            case .serverError(404):
                Log.info("ℹ️ 设备 \(deviceId) 不存在或无传感器状态数据")
                sensorStatusErrors[deviceId] = SensorStatusError(message: "设备不存在")
            case .unauthorized:
                Log.warning("⚠️ 设备传感器状态获取授权失败，NetworkManager已完成静默重试")
                sensorStatusErrors[deviceId] = SensorStatusError(message: "认证失败，请重新登录")
            case .noData:
                Log.info("ℹ️ 设备 \(deviceId) 传感器状态暂无数据")
                // 创建空的传感器状态
                let emptySensorStatus = DeviceSensorStatusResponse(
                    deviceId: deviceId,
                    cameraLastErrorType: nil,
                    cameraLastErrorTime: nil,
                    weightSensorLastErrorType: nil,
                    weightSensorLastErrorTime: nil,
                    wifiLastErrorType: nil,
                    wifiLastErrorTime: nil,
                    microphoneLastErrorType: nil,
                    microphoneLastErrorTime: nil,
                    bluetoothLastErrorType: nil,
                    bluetoothLastErrorTime: nil,
                    temperatureHumiditySensorLastErrorType: nil,
                    temperatureHumiditySensorLastErrorTime: nil,
                    lastUpdated: DateFormatter.iso8601.string(from: Date())
                )
                sensorStatuses[deviceId] = emptySensorStatus
            case .timeoutError:
                sensorStatusErrors[deviceId] = SensorStatusError(message: "网络超时，请稍后重试")
            case .connectivityError:
                sensorStatusErrors[deviceId] = SensorStatusError(message: "网络连接异常，请检查网络")
            default:
                sensorStatusErrors[deviceId] = SensorStatusError(message: "获取传感器状态失败: \(error.localizedDescription)")
            }
            
            Log.error("❌ 获取设备 \(deviceId) 传感器状态失败: \(error.localizedDescription)")
        } catch {
            sensorStatusErrors[deviceId] = SensorStatusError(message: "获取传感器状态失败: \(error.localizedDescription)")
            Log.error("❌ 获取设备 \(deviceId) 传感器状态失败: \(error.localizedDescription)")
        }
        
        isFetchingSensorStatus.remove(deviceId)
    }
    
    /// 批量获取设备传感器状态
    @MainActor
    func fetchSensorStatuses(for deviceIds: [String]) async {
        await withTaskGroup(of: Void.self) { group in
            for deviceId in deviceIds {
                group.addTask {
                    await self.fetchSensorStatus(for: deviceId)
                }
            }
        }
    }
    
    /// 刷新所有设备的传感器状态
    @MainActor
    func refreshAllSensorStatuses() async {
        let deviceIds = devices.map { $0.id }
        await fetchSensorStatuses(for: deviceIds)
    }
    
    /// 刷新所有设备状态
    @MainActor
    func refreshAllDevicesStatus() async {
        guard !devices.isEmpty else {
            // 如果设备列表为空，强制获取设备列表以确保获取最新数据
            await fetchAccessibleDevices(forceRefresh: true)
            return
        }
        
        // 检查是否正在刷新
        guard !isRefreshInProgress.withLock({ $0 }) else {
            Log.debug("🔄 设备状态刷新已在进行中，跳过重复请求")
            return
        }
        
        Log.info("🔄 开始刷新所有设备状态")
        isRefreshInProgress.withLock { $0 = true }
        defer { isRefreshInProgress.withLock { $0 = false } }
        
        var updatedDevices: [DeviceStatusResponse] = []
        
        for device in devices {
            if let updatedStatus = await fetchDeviceStatus(deviceId: device.id) {
                // 创建更新后的设备状态
                let newStatus = DeviceStatusResponse(
                    id: device.id,
                    name: updatedStatus.name,
                    model: updatedStatus.model,
                    firmware: updatedStatus.firmware,
                    online: updatedStatus.online,
                    lastHeartbeat: updatedStatus.lastHeartbeat,
                    ipv4: updatedStatus.ipv4,
                    ipv6: updatedStatus.ipv6
                )
                updatedDevices.append(newStatus)
            } else {
                // 如果无法获取最新状态，保留原状态
                updatedDevices.append(device)
            }
        }
        
        // 更新设备列表
        self.devices = updatedDevices
        lastRefreshTime = Date()
        
        // 🔄 应用排序 - 在线设备优先显示
        applySortingToDevices()
        
        // 保存到缓存
        saveDevicesToCache()
        
        Log.info("✅ 设备状态刷新完成，共更新 \(updatedDevices.count) 台设备")
    }
    
    /// 获取单个设备的状态 - 基于test_device_status.sh中的API
    @MainActor
    func fetchDeviceStatus(deviceId: String) async -> DeviceStatusResponse? {
        Log.debug("🔍 开始获取设备状态: \(deviceId)")
        
        do {
            // 构建API URL
            let urlString = Configuration.API.Device.getDeviceStatusPath(deviceId: deviceId)
            guard let url = URL(string: urlString) else {
                Log.error("❌ 无效的设备状态API URL: \(urlString)")
                return nil
            }
            
            // 使用NetworkManager统一处理请求
            let deviceStatus: DeviceStatusResponse = try await NetworkManager.shared.request(url)
            
            Log.debug("✅ 设备状态获取成功: 设备=\(deviceStatus.name), 在线=\(deviceStatus.online == true ? "是" : deviceStatus.online == false ? "否" : "未知")")
            
            return deviceStatus
            
        } catch {
            Log.error("❌ 获取设备状态失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 更新单个设备状态 - 手动刷新特定设备
    @MainActor
    func updateDeviceStatus(deviceId: String) async {
        guard let statusResponse = await fetchDeviceStatus(deviceId: deviceId) else {
            Log.error("❌ 无法获取设备 \(deviceId) 的状态")
            return
        }
        
        // 在设备列表中找到对应的设备并更新其状态
        if let index = devices.firstIndex(where: { $0.id == deviceId }) {
            let updatedDevice = DeviceStatusResponse(
                id: deviceId,
                name: statusResponse.name,
                model: statusResponse.model,
                firmware: statusResponse.firmware,
                online: statusResponse.online,
                lastHeartbeat: statusResponse.lastHeartbeat,
                ipv4: statusResponse.ipv4,
                ipv6: statusResponse.ipv6
            )
            
            // 更新设备列表
            devices[index] = updatedDevice
            
            Log.info("✅ 设备状态已更新: \(deviceId) -> \(statusResponse.online == true ? "在线" : statusResponse.online == false ? "离线" : "状态未知")")
        } else {
            Log.warning("⚠️ 在设备列表中未找到设备: \(deviceId)")
        }
    }
    
    /// 将设备添加到家庭组
    @MainActor
    func addDeviceToGroup(deviceId: String, groupId: String) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "DeviceManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取用户ID"])
        }
        
        // 构建API URL
        let urlString = Configuration.API.FamilyGroup.addDevicePath(groupId: groupId)
        guard let url = URL(string: urlString) else {
            throw NetworkError.invalidURL
        }
        
        // 创建请求体
        let requestBody: [String: Any] = [
            "device_id": deviceId,
            "user_id": userId
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 使用NetworkManager发送POST请求
        let _: EmptyResponse = try await NetworkManager.shared.post(url, body: jsonData)
        
        Log.info("✅ 设备成功添加到家庭组")
    }
    
    // MARK: - Family Group Management
    
    /// 获取当前用户的家庭组列表
    @MainActor
    func fetchFamilyGroups() async {
        // 获取当前用户ID
        guard let userId = UserDefaultsManager.shared.userId else { 
            Log.error("❌ 无法获取用户ID")
            return 
        }
        
        Log.info("🏠 开始获取用户ID：\(userId) 的家庭组")
        isFetchingGroups = true
        
        do {
            // 构建API URL
            let urlString = Configuration.API.FamilyGroup.getFamilyGroupsPath(userId: userId)
            guard let url = URL(string: urlString) else {
                throw NetworkError.invalidURL
            }
            
            // 使用NetworkManager进行请求
            let responseData: FamilyGroupsResponse = try await NetworkManager.shared.request(url)
            
            Log.info("📡 家庭组API响应状态: 成功")
            Log.info("📋 家庭组API响应: \(responseData.status) - \(responseData.message)")
            
            if let familyGroups = responseData.data {
                Log.info("📋 解析家庭组数据成功: 状态=\(responseData.status), 消息=\(responseData.message), 家庭组数量=\(familyGroups.count)")
                
                // 将数据转换为FamilyGroup模型
                let fetchedGroups = familyGroups.map { groupData in
                    // 根据是否是拥有者来确定角色
                    let role: FamilyGroupRole
                    if groupData.ownerId == userId {
                        role = .owner
                    } else {
                        role = .member
                    }
                    
                    return FamilyGroup(
                        id: groupData.groupId,
                        name: groupData.groupName,
                        description: groupData.description,
                        memberCount: groupData.memberCount,
                        deviceCount: groupData.deviceCount,
                        role: role
                    )
                }
                
                Log.info("📋 成功转换 \(fetchedGroups.count) 个家庭组数据")
                
                // 更新家庭组列表
                self.familyGroups = fetchedGroups
                
            } else {
                Log.info("📋 服务器返回空的家庭组列表，用户可能没有加入任何家庭组")
                self.familyGroups = []
            }
            
        } catch {
            Log.error("❌ 获取家庭组失败: \(error.localizedDescription)")
            self.familyGroups = []
        }
        
        isFetchingGroups = false
    }
    
    /// 创建家庭组
    @MainActor
    func createFamilyGroup(groupName: String, description: String) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "DeviceManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取用户ID"])
        }
        
        // 构建API URL - 确保包含query参数
        let baseUrl = Configuration.API.FamilyGroup.createFamilyGroupPath()
        var urlComponents = URLComponents(string: baseUrl)
        
        // 添加user_id作为query参数
        urlComponents?.queryItems = [
            URLQueryItem(name: "user_id", value: userId)
        ]
        
        guard let url = urlComponents?.url else {
            throw NetworkError.invalidURL
        }
        
        Log.info("🔗 创建家庭组API URL: \(url.absoluteString)")
        
        // 创建请求体
        let requestBody: [String: Any] = [
            "group_name": groupName,
            "description": description
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 使用NetworkManager发送POST请求
        let groupResponse: CreateFamilyGroupResponse = try await NetworkManager.shared.post(url, body: jsonData)
        
        Log.info("✅ 家庭组创建成功: ID=\(groupResponse.data.groupId), 名称=\(groupResponse.data.groupName)")
    }
    
    // MARK: - Invitation Management
    
    /// 获取邀请列表
    @MainActor
    func fetchInvitations() async {
        guard let userId = UserDefaultsManager.shared.userId else {
            Log.error("❌ 未找到用户ID")
            return
        }
        
        isLoadingInvitations = true
        
        do {
            // 构建API URL
            let urlString = Configuration.API.FamilyGroup.Invitation.getReceivedInvitationsPath(userId: userId)
            guard let url = URL(string: urlString) else {
                throw NSError(domain: "InviteError", code: -1, 
                             userInfo: [NSLocalizedDescriptionKey: "无效的API URL"])
            }
            
            Log.info("🔗 邀请列表API URL: \(urlString)")
            
            // 使用NetworkManager发送请求
            let invitationResponse: InvitationsResponse = try await NetworkManager.shared.request(url)
            
            Log.info("📊 邀请列表API响应状态码: 成功")
            Log.info("📋 邀请列表API响应: \(invitationResponse.status) - \(invitationResponse.message)")
            
            // 处理 data 可能为 null 的情况
            if let invitationData = invitationResponse.data {
                self.invitations = invitationData
                Log.info("✅ 成功获取 \(invitationData.count) 条邀请")
            } else {
                self.invitations = [] // 如果 data 为 null，设置为空数组
                Log.info("✅ 没有待处理的邀请")
            }
            
        } catch {
            Log.error("❌ 获取邀请列表失败: \(error.localizedDescription)")
        }
        
        isLoadingInvitations = false
    }
    
    /// 处理邀请（接受或拒绝）
    @MainActor
    func processInvitation(_ invitation: InvitationResponse, action: String) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "InviteError", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "未找到用户ID"])
        }
        
        // 构建基础URL路径
        let basePath = Configuration.API.FamilyGroup.Invitation.processInvitationPath(
            invitationId: invitation.invitationId,
            action: ""  // 清空action参数，我们将在下面手动添加所有query参数
        )
        
        // 移除末尾的"action="（如果存在）
        let cleanBasePath = basePath.replacingOccurrences(of: "action=", with: "")
        
        // 手动构建完整URL，确保包含所有必要的query参数
        var components = URLComponents(string: cleanBasePath)
        
        // 添加所有必需的query参数
        components?.queryItems = [
            URLQueryItem(name: "user_id", value: userId),
            URLQueryItem(name: "action", value: action)
        ]
        
        guard let url = components?.url else {
            throw NSError(domain: "InviteError", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "无效的API URL"])
        }
        
        Log.info("🔗 处理邀请API URL: \(url.absoluteString)")
        
        // 使用NetworkManager发送PUT请求
        let _: EmptyResponse = try await NetworkManager.shared.put(url, body: nil)
        
        Log.info("✅ 成功\(action == "accept" ? "接受" : "拒绝")邀请")
        
        // 重新获取邀请列表
        await fetchInvitations()
    }
    
    // MARK: - Device Basic Info Management

    /// 更新设备基本信息 - 使用新的API端点
    @MainActor
    func updateDeviceBasicInfo(deviceId: String, name: String? = nil, model: String? = nil, timezone: String? = nil, firmwareVersion: String? = nil, status: Int? = nil) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "DeviceManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取用户ID"])
        }

        // 构建API URL - 使用新的basic-info端点
        let urlString = "\(Configuration.API.cabyBaseUrl)/api/devices/\(deviceId)/basic-info?user_id=\(userId)"
        guard let url = URL(string: urlString) else {
            throw NetworkError.invalidURL
        }

        // 创建请求体 - 只包含需要更新的字段
        var requestBody: [String: Any] = [:]

        if let name = name {
            requestBody["name"] = name
        }
        if let model = model {
            requestBody["model"] = model
        }
        if let timezone = timezone {
            requestBody["timezone"] = timezone
        }
        if let firmwareVersion = firmwareVersion {
            requestBody["firmware_version"] = firmwareVersion
        }
        if let status = status {
            requestBody["status"] = status
        }

        // 如果没有要更新的字段，直接返回
        guard !requestBody.isEmpty else {
            Log.warning("⚠️ 没有要更新的设备信息字段")
            return
        }

        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)

        Log.info("📤 更新设备基本信息: \(deviceId)")
        Log.info("📤 更新字段: \(requestBody.keys.joined(separator: ", "))")

        // 使用NetworkManager发送PUT请求
        let response: UpdateDeviceBasicInfoResponse = try await NetworkManager.shared.put(url, body: jsonData)

        Log.info("✅ 设备基本信息更新成功: \(response.message)")

        // 更新本地设备列表中的设备信息
        if let index = devices.firstIndex(where: { $0.id == deviceId }) {
            let currentDevice = devices[index]
            let updatedDevice = DeviceStatusResponse(
                id: deviceId,
                name: name ?? currentDevice.name,
                model: model ?? currentDevice.model,
                firmware: firmwareVersion ?? currentDevice.firmware,
                online: currentDevice.online,
                lastHeartbeat: currentDevice.lastHeartbeat,
                ipv4: currentDevice.ipv4,
                ipv6: currentDevice.ipv6
            )

            devices[index] = updatedDevice
            applySortingToDevices()
            saveDevicesToCache()

            Log.info("🔄 本地设备信息已更新: \(updatedDevice.name)")
        }
    }

    /// 更新设备名称 - 便捷方法
    @MainActor
    func updateDeviceName(deviceId: String, newName: String) async throws {
        try await updateDeviceBasicInfo(deviceId: deviceId, name: newName)
    }

    // MARK: - Helper Methods

    /// 将角色ID转换为文本
    func roleText(for roleId: Int) -> String {
        switch roleId {
        case 1:
            return "拥有者"
        case 2:
            return "管理员"
        case 3:
            return "成员"
        default:
            return "未知角色(\(roleId))"
        }
    }
    
    /// 将状态ID转换为文本
    func statusText(for statusId: Int) -> String {
        switch statusId {
        case 0:
            return "待处理"
        case 1:
            return "已接受"
        case 2:
            return "已拒绝"
        case 3:
            return "已取消"
        default:
            return "未知状态(\(statusId))"
        }
    }
    
    // MARK: - OTA设置管理
    
    /// 获取设备OTA设置
    func fetchDeviceOTASettings(deviceId: String) async -> DeviceOTASettings? {
        guard let userId = UserDefaultsManager.shared.userId else {
            Log.warning("⚠️ 未找到用户ID")
            return nil
        }
        
        guard let url = URL(string: "\(Configuration.API.cabyBaseUrl)/api/devices/\(deviceId)/setting?user_id=\(userId)") else {
            Log.error("❌ 无效的OTA设置URL")
            return nil
        }
        
        do {
            // 使用NetworkManager发送GET请求
            // 首先尝试解析标准格式（包含status, message, data）
            do {
                let response: OTASettingsResponse = try await NetworkManager.shared.request(url)
                Log.info("✅ 获取设备OTA设置成功（标准格式）: \(response.data.deviceId)")
                return response.data
            } catch {
                Log.info("⚠️ 标准格式解析失败，尝试简单格式")
                
                // 尝试解析简单格式（只有auto_ota_upgrade字段）
                do {
                    let simpleResponse: SimpleOTASettings = try await NetworkManager.shared.request(url)
                    Log.info("✅ 获取设备OTA设置成功（简单格式）: auto_ota_upgrade=\(simpleResponse.autoOtaUpgrade)")
                    
                    // 转换为标准格式
                    let standardSettings = DeviceOTASettings(from: simpleResponse, deviceId: deviceId)
                    return standardSettings
                } catch {
                    Log.error("❌ 简单格式解析也失败: \(error.localizedDescription)")
                    return nil
                }
            }
            
        }
    }
    
    /// 更新设备OTA设置
    func updateDeviceOTASettings(deviceId: String, request: DeviceOTAUpdateRequest) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NetworkError.unauthorized
        }
        
        guard let url = URL(string: "\(Configuration.API.cabyBaseUrl)/api/devices/\(deviceId)/setting?user_id=\(userId)") else {
            throw NetworkError.invalidURL
        }
        
        // 🆕 根据是否需要时间参数使用不同的请求格式
        let simpleRequest = SimpleOTAUpdateRequest(
            enabled: request.autoOtaEnabled,
            idleUpdateStartHour: request.idleUpdateStartHour,
            idleUpdateEndHour: request.idleUpdateEndHour
        )
        
        // 设置请求体
        let encoder = JSONEncoder()
        let jsonData = try encoder.encode(simpleRequest)
        
        // 打印请求体以调试
        if let requestString = String(data: jsonData, encoding: .utf8) {
            Log.info("📤 OTA设置更新请求体: \(requestString)")
        }
        
        Log.info("📤 更新设备OTA设置: \(deviceId)")
        
        // 使用NetworkManager发送PUT请求
        let _: EmptyResponse = try await NetworkManager.shared.put(url, body: jsonData)
        
        Log.info("✅ 更新设备OTA设置成功")
    }

    /// 创建邀请 - 新增方法
    @MainActor
    func createInvitation(groupId: String, inviteeEmail: String, role: Int, message: String) async throws {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "InviteError", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "无法获取用户ID"])
        }
        
        // 构建API URL - 修改为包含query参数
        var urlComponents = URLComponents(string: Configuration.API.FamilyGroup.Invitation.createEmailInvitationPath(groupId: groupId))
        
        // 添加user_id作为query参数
        urlComponents?.queryItems = [
            URLQueryItem(name: "user_id", value: userId)
        ]
        
        // 确保URL生成成功
        guard let url = urlComponents?.url else {
            throw NetworkError.invalidURL
        }
        
        Log.info("🔗 创建邀请API URL: \(url)")
        
        // 准备请求体 - 修改参数名称
        let invitationData: [String: Any] = [
            "invitee_email": inviteeEmail,  // 修改为正确的参数名
            "role": role,
            "message": message
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: invitationData)
        
        // 日志记录请求体内容
        Log.info("📤 创建邀请请求体: \(invitationData)")
        Log.info("📤 创建邀请URL: \(url.absoluteString)")
        
        // 使用NetworkManager发送POST请求
        let _: EmptyResponse = try await NetworkManager.shared.post(url, body: jsonData)
        
        Log.info("✅ 邀请创建成功")
    }

    /// 获取指定家庭组的成员列表 - 从DeviceAndGroupManagementView中移过来的逻辑
    @MainActor
    func fetchFamilyGroupMembers(groupId: String) async throws -> [FamilyGroup] {
        guard let userId = UserDefaultsManager.shared.userId else {
            throw NSError(domain: "DeviceManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取用户ID"])
        }
        
        // 构建API URL
        let urlString = Configuration.API.FamilyGroup.getFamilyGroupsPath(userId: userId)
        guard let url = URL(string: urlString) else {
            throw NetworkError.invalidURL
        }
        
        // 使用NetworkManager发送请求
        let responseData: FamilyGroupsResponse = try await NetworkManager.shared.request(url)
        
        if let familyGroups = responseData.data {
            // 将数据转换为FamilyGroup模型
            let fetchedGroups = familyGroups.map { groupData in
                let role: FamilyGroupRole = groupData.ownerId == userId ? .owner : .member
                
                return FamilyGroup(
                    id: groupData.groupId,
                    name: groupData.groupName,
                    description: groupData.description,
                    memberCount: groupData.memberCount,
                    deviceCount: groupData.deviceCount,
                    role: role
                )
            }
            
            return fetchedGroups
        } else {
            return []
        }
    }
} 
