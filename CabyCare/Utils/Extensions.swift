import Foundation
import SwiftUI

// MARK: - Notification Names
extension Notification.Name {
    static let avatarUpdated = Notification.Name("avatarUpdated")
    static let dataRefreshedOnForeground = Notification.Name("DataRefreshedOnForegroundNotification")

    /// 设备缓存清除通知
    static let deviceCacheCleared = Notification.Name("deviceCacheCleared")

    /// 设备名称更新通知 - 用于同步视频页面和设备页面的设备名称显示
    static let deviceNameUpdated = Notification.Name("deviceNameUpdated")
}

// MARK: - Date Extensions
extension Date {
    func formattedString(format: String = "yyyy-MM-dd HH:mm:ss") -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.string(from: self)
    }
    
    var startOfDay: Date {
        let calendar = Calendar.current
        return calendar.startOfDay(for: self)
    }
    
    var endOfDay: Date {
        let calendar = Calendar.current
        var components = DateComponents()
        components.day = 1
        components.second = -1
        return calendar.date(byAdding: components, to: startOfDay) ?? self
    }
    
    func toLocalizedString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale.current
        return formatter.string(from: self)
    }
    
    func toRelativeString() -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: self, relativeTo: Date())
    }
}

// MARK: - DateFormatter Extensions
extension DateFormatter {
    static let iso8601: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        formatter.locale = Locale(identifier: "en_US_POSIX")
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }()
}

// MARK: - Color Extensions
extension Color {
    static let systemBackground = Color(UIColor.systemBackground)
    static let secondarySystemBackground = Color(UIColor.secondarySystemBackground)
    static let deviceOnline = Color.green
    static let deviceOffline = Color.red
    static let deviceUnknown = Color.gray
    
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    func deviceStatusIndicator(_ status: DeviceStatus) -> some View {
        self.overlay(
            Circle()
                .fill(status.color)
                .frame(width: 8, height: 8),
            alignment: .topTrailing
        )
    }
    
    func refreshIndicator(isRefreshing: Bool) -> some View {
        self.overlay(
            Group {
                if isRefreshing {
                    ProgressView()
                        .scaleEffect(0.6)
                        .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                }
            },
            alignment: .topLeading
        )
    }
}

// MARK: - String Extensions
extension String {
    func localized() -> String {
        return NSLocalizedString(self, comment: "")
    }
    
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

// MARK: - DeviceStatus Extensions
extension DeviceStatus {
    var color: Color {
        switch self {
        case .online:
            return .deviceOnline
        case .offline:
            return .deviceOffline
        case .unknown:
            return .deviceUnknown
        }
    }
    
    var localizedDescription: String {
        switch self {
        case .online:
            return NSLocalizedString("device_status_online", comment: "在线")
        case .offline:
            return NSLocalizedString("device_status_offline", comment: "离线")
        case .unknown:
            return NSLocalizedString("device_status_unknown", comment: "未知")
        }
    }
}
