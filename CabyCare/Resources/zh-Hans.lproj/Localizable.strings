"add_cat" = "添加猫咪";
"add_device" = "添加设备";

/* Caby */
"cabycare_title" = "CabyCare";
"cabycare_slogan" = "live different with your cats";

/* Login View */
"login_button" = "开始使用";
"login_error" = "登录失败";
"login_retry" = "重试";
"login_loading" = "正在载入...";
"login_relogin" = "重新登录";
"login_relogin_title" = "会话已过期";
"login_relogin_message" = "您的会话已过期，请重新登录";

/* Animals View */
"animals_title" = "我的猫咪";
"animals_no_cats_message" = "您还没有添加猫咪";
"animals_loading" = "正在加载...";
"animals_error_title" = "加载失败";
"animals_retry" = "重试";

/* Cat Profile Creation */
"cat_creation_title" = "创建猫咪档案";
"cat_creation_intro_title" = "创建猫咪档案";
"cat_creation_intro_description" = "请您尽可能地提供正确的信息，这有助于我们更好地服务猫咪。\n\n如果您不确定，可以留空。";

"cat_creation_name_label" = "名字";
"cat_creation_gender_label" = "性别";
"cat_creation_neutered_label" = "绝育状态";
"cat_creation_male" = "公猫";
"cat_creation_female" = "母猫";
"cat_creation_unknown" = "未知";
"cat_creation_neutered" = "已绝育";
"cat_creation_not_neutered" = "未绝育";
"cat_creation_weight_label" = "体重";
"cat_creation_weight_unit" = "kg";
"cat_creation_birth_date_label" = "出生日期";
"cat_creation_age_label" = "年龄";
"cat_creation_age_year" = "岁";
"cat_creation_age_month" = "月";
"cat_creation_age_less_than_month" = "不到1个月";

"cat_creation_photos_description" = "请尽量提供只有[replace_to_cat_name]的照片以提高准确性。\n包含多只猫的照片可能会被Caby自动过滤掉。";
"cat_creation_max_photos" = "最多9张照片";

"cat_creation_processing_title" = "正在创建猫咪档案...";
"cat_creation_success_title" = "档案创建成功！";
"cat_creation_success_description" = "您的猫咪档案已成功创建。";

/* Chart Related */
"chart_period_day" = "天";
"chart_period_week" = "周";
"chart_period_month" = "月";
"chart_trend_increasing" = "上升";
"chart_trend_decreasing" = "下降";
"chart_trend_stable" = "稳定";

"cat_creation_basic_info" = "基本信息";
"cat_creation_physical_info" = "身体特征";
"cat_creation_full_info" = "猫咪档案";
"cat_creation_photo_info" = "照片";

"cat_creation_previous" = "上一步";
"cat_creation_next" = "下一步";
"cat_creation_preview" = "预览";
"cat_creation_submit" = "提交";
"cat_creation_cancel" = "取消";
"cat_creation_confirm" = "确认";
"cat_creation_finish" = "完成";
"cat_creation_submitting" = "正在提交...";
"cat_creation_name_required" = "* 名字为必填项";
"cat_creation_skip" = "这些字段是可选的。您可以跳到下一步，但提供准确的信息将帮助Caby做出更好的判断。";

"cat_creation_cancel_confirmation_title" = "取消创建档案？";
"cat_creation_cancel_confirmation_message" = "确定要取消吗？所有已输入的信息将会丢失。";

"cat_creation_invalid_photos_title" = "无效照片";
"cat_creation_invalid_photos_message" = "某些照片无法验证。请确保照片清晰地显示您的猫咪。";

/* Errors */
"error_title" = "错误";
"ok" = "确定";
"cat_creation_error_invalid_name" = "请务必赋予猫咪一个名字";
"cat_creation_error_name_too_long" = "名字过长（最多256个字符）";
"cat_creation_error_invalid_weight" = "如果不知道猫咪的体重，可以选择**我不清楚**";
"cat_creation_error_invalid_photo" = "照片验证失败，请重新选择";
"cat_creation_error_submission" = "提交档案失败，请重试。"; 
"cat_creation_error_name_invalid" = "名字无效";
"cat_creation_error_no_photos" = "请至少添加一张有效的照片";

// Cat profile related
"cat_neutered_status" = "已绝育";
"cat_health_status_label" = "健康状况";
"cat_profile_description" = "通过CabyCare创建的猫咪档案";
"cat_avatar_label" = "头像";
"cat_gender_male" = "公猫";
"cat_gender_female" = "母猫";

// Cat status values
"cat_status_unknown" = "未知";
"cat_status_not_neutered" = "未绝育";
"cat_weight_unknown" = "未知";
"cat_age_unknown" = "未知";

// Health status
"cat_health_healthy" = "健康";
"cat_health_needs_attention" = "需要关注";
"cat_health_sick" = "生病";

// Settings
"settings_title" = "设置";
"settings_logout" = "退出登录";
"settings_logout_message" = "确定要退出登录吗？";
"settings_logout_confirm" = "退出登录";
"settings_logout_cancel" = "取消";

// Notification settings
"notification_settings_title" = "通知设置";
"notification_daily_reminder" = "日常提醒";
"notification_daily_reminder_description" = "包括猫咪如厕、饮食等日常行为提醒";
"notification_health_alert" = "健康提醒";
"notification_health_description" = "猫咪健康状况异常时的提醒";
"notification_weekly_reports" = "包括每周活动报告和健康统计信息";
"notification_health_safety_notice" = "这些通知类型关系到猫咪的健康和安全，不能关闭";
"notification_statistics_alert" = "统计提醒";
"notification_abnormal_alert" = "异常提醒";
"notification_abnormal_description" = "检测到异常行为或可疑物体时的提醒";
"notification_maintenance_alert" = "维护提醒";
"notification_maintenance_description" = "设备维护和清理相关的提醒";
"notification_important_title" = "重要通知";
"notification_always_on" = "始终开启";
"notification_types_title" = "通知类型";
"notification_quiet_time" = "免打扰时间";
"notification_start_time" = "开始时间";
"notification_end_time" = "结束时间";
"notification_current_setting" = "当前设置:";
"notification_quiet_time_footer" = "在设定的时间段内，只有紧急通知会发出提醒";
"notification_cancel" = "取消";
"notification_save" = "保存";
"notification_settings_footer" = "在设定的时间段内，只有紧急通知会发出提醒";
"notification_enabled_count" = "已启用";

// Home View
"home_title" = "首页";
"home_today_overview" = "今日概览";
"home_recent_notifications" = "最新提醒";
"home_interaction_time" = "互动时长";
"home_feeding_count" = "喂食次数";
"home_activity_level" = "活跃度";
"home_feeding_reminder" = "喂食提醒";
"home_feeding_message" = "该给小咪准备晚餐啦";
"home_interaction_reminder" = "互动提醒";
"home_interaction_message" = "今天还没有和小咪玩耍哦";
"home_health_reminder" = "健康提醒";
"home_health_message" = "该给小咪清理猫砂了";
"home_time_minutes_ago" = "分钟前";
"home_time_hour_ago" = "小时前";

// New Home View - Toilet Statistics
"home_alert_title" = "重点关注";
"home_toilet_stats_title" = "最近24小时统计";
"home_toilet_count" = "如厕次数";
"home_toilet_duration" = "总时长";
"home_weight_change" = "体重变化";
"home_loading_data" = "正在加载数据...";
"home_calculating_stats" = "正在统计中...";
"home_calculating_message" = "正在计算猫咪统计数据，请稍候";
"home_no_data_title" = "暂无数据";
"home_no_data_message" = "当有如厕记录时，这里会显示统计信息";
"home_comparison_vs" = "vs";
"home_comparison_info_title" = "环比说明";
"home_comparison_info_message" = "显示最近24小时与前24小时的数据对比";
"home_weight_change_info_title" = "体重变化说明";
"home_weight_change_info_message" = "显示相比前一天的体重变化百分比。正数表示体重增加，负数表示体重减少。建议定期关注猫咪体重变化趋势。";
"home_comparison_previous_period" = "环比前一周期";
"home_duration_minutes" = "分钟";
"home_no_toilet_records" = "暂无如厕记录";
"home_hours_no_toilet" = "小时没有如厕了";
"home_alert_no_toilet_prefix" = "";
"home_alert_no_toilet_suffix" = "已经";
"home_alert_hours_suffix" = "小时没有如厕了";

// Welcome Messages for First Time Users
"home_welcome_title" = "欢迎使用 CabyCare";
"home_welcome_message" = "开始监测您的爱猫健康";
"home_welcome_instruction" = "请确保设备已连接并有猫咪使用记录";
"home_setup_step1" = "确保设备已正确连接";
"home_setup_step2" = "添加您的猫咪档案";
"home_setup_step3" = "等待猫咪使用设备";

// Cat Management
"animals_tab_normal" = "正常";
"animals_tab_hidden" = "隐藏";
"animals_loading_hidden" = "正在加载隐藏的猫咪...";
"animals_no_hidden_cats_message" = "没有隐藏的猫咪";

// Cat Actions
"cat_edit_menu_title" = "编辑猫咪";
"cat_action_edit" = "编辑";
"cat_action_hide" = "隐藏";
"cat_action_restore" = "恢复";
"cat_action_delete" = "删除";
"cat_delete_confirmation_title" = "删除猫咪";
"cat_delete_confirmation_message" = "确定要删除这只猫咪吗？删除后将无法恢复。";
"cancel" = "取消";

// Cat Edit
"cat_edit_title" = "编辑猫咪档案";
"cat_edit_save" = "保存";

// Device & Group Management
"device_and_group_title" = "设备与家庭组";
"device_group_picker_label" = "选择视图";
"device_management_tab" = "设备管理";
"family_group_tab" = "家庭组";
"create_new_group" = "创建新家庭组";
"invite_user_to_group" = "邀请用户加入家庭组";

// Device Status & Info
"current_selected_device" = "当前选中设备";
"firmware_version" = "固件版本: %@";
"device_status_online" = "在线";
"device_status_offline" = "离线";
"view_details" = "查看详情";
"auto_refresh" = "自动刷新";
"auto_refreshing" = "自动刷新中";
"select_device_prompt" = "请从上方列表中选择一个设备";
"refresh_device_status" = "刷新设备状态";

// Group Info
"group_member_count" = "%d 位成员";
"group_device_count" = "%d 台设备";
"select_group_prompt" = "请从上方列表中选择一个家庭组";
"group_sharing_hint" = "家庭组中的成员可以共享设备数据";

// Group Roles
"group_role_owner" = "拥有者";
"group_role_admin" = "管理员";
"group_role_member" = "成员";

// Device History & Sharing
"device_history_title" = "%@的历史数据";
"device_history_development" = "功能开发中...";
"device_history_nav_title" = "历史数据";
"device_sharing_title" = "%@的共享设置";
"device_sharing_development" = "功能开发中...";
"device_sharing_nav_title" = "共享设置";

// Add Device
"back" = "返回";
"add_device_method_title" = "选择设备添加方式";
"add_device_method_description" = "请选择最适合您的设备添加方式";

// Bluetooth Configuration
"bluetooth_config_device_title" = "蓝牙配置设备";
"bluetooth_config_device_subtitle" = "自动发现并配置AbyBox设备";
"bluetooth_config_device_description" = "通过蓝牙连接，自动配置WiFi和用户信息";
"recommend_bluetooth_config" = "推荐使用蓝牙配置";
"bluetooth_config_benefits" = "蓝牙配置更快捷、更准确";
"recommended" = "推荐";

// Manual Add Device
"manual_add_device_title" = "手动添加设备";
"manual_add_device_subtitle" = "输入设备信息手动添加";
"manual_add_device_description" = "通过手动输入设备信息进行添加";
"device_info_section" = "设备信息";
"device_name_placeholder" = "设备名称";
"device_location_placeholder" = "设备位置";
"adding_device" = "正在添加设备";
"add" = "添加";
"success" = "成功";
"device_add_success" = "设备 %@ 添加成功";
/* Video related strings */
"video_cat_unknown" = "未知猫咪";

// Video Duration
"video_duration_minutes_only" = "%d分钟";
"video_duration_minutes_seconds" = "%d分%d秒";
"video_duration_seconds_only" = "%d秒";

// Summary Duration
"summary_duration_hours_minutes" = "%d小时%d分钟";
"summary_duration_minutes_only" = "%d分钟";

/* Chart Data Types */
"chart_data_toilet" = "如厕次数";
"chart_data_weight" = "重量";

/* Chart Units */
"chart_unit_times" = "次";
"chart_unit_kg" = "kg";
