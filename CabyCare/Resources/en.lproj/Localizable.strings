"add_cat" = "Add a Cat";
"add_device" = "Add a Device";

/* Caby */
"cabycare_title" = "CabyCare";
"cabycare_slogan" = "live different with your cats";

/* Login View */
"login_button" = "Get Started";
"login_error" = "Login Failed";
"login_retry" = "Retry";
"login_loading" = "Loading...";
"login_relogin_title" = "Session Expired";
"login_relogin" = "Re-login";
"login_relogin_message" = "Your session has expired, please re-login";

/* Animals View */
"animals_title" = "My Cats";
"animals_no_cats_message" = "You haven't added any cats yet";
"animals_loading" = "Loading...";
"animals_error_title" = "Failed to Load";
"animals_retry" = "Retry";

/* Cat Profile Creation */
"cat_creation_title" = "Create Cat Profile";
"cat_creation_intro_title" = "Create a Cat Profile";
"cat_creation_intro_description" = "Please make sure you give us the right info, as it'll help us to look after your cat better.\n\nIf you're unsure about any details, just leave them blank.";

"cat_creation_name_label" = "Name";
"cat_creation_gender_label" = "Gender";
"cat_creation_neutered_label" = "Neutering Status";
"cat_creation_male" = "Male";
"cat_creation_female" = "Female";
"cat_creation_unknown" = "Unknown";
"cat_creation_neutered" = "Neutered";
"cat_creation_not_neutered" = "Not Neutered";
"cat_creation_weight_label" = "Weight";
"cat_creation_weight_unit" = "kg";
"cat_creation_birth_date_label" = "Birth Date";
"cat_creation_age_label" = "Age";
"cat_creation_age_year" = " Years";
"cat_creation_age_month" = " Months";
"cat_creation_age_less_than_month" = "less than 1 month";
"cat_creation_photos_description" = "Please try to provide photos with ONLY [replace_to_cat_name] in them to improve accuracy. \nPhotos with multiple cats may be automatically filtered out by Caby.";
"cat_creation_max_photos" = "Maximum 9 photos";

"cat_creation_processing_title" = "Creating Cat Profile...";
"cat_creation_success_title" = "Profile Created!";
"cat_creation_success_description" = "Your cat's profile has been successfully created.";

/* Chart Related */
"chart_period_day" = "Day";
"chart_period_week" = "Week";
"chart_period_month" = "Month";
"chart_trend_increasing" = "Increasing";
"chart_trend_decreasing" = "Decreasing";
"chart_trend_stable" = "Stable";

"cat_creation_basic_info" = "Basic Information";
"cat_creation_physical_info" = "Physical Information";
"cat_creation_full_info" = "Cat Profile";
"cat_creation_photo_info" = "Photos";

"cat_creation_previous" = "Previous";
"cat_creation_next" = "Next";
"cat_creation_preview" = "Preview";
"cat_creation_submit" = "Submit";
"cat_creation_cancel" = "Cancel";
"cat_creation_confirm" = "Confirm";
"cat_creation_finish" = "Finish";
"cat_creation_submitting" = "Submitting...";

"cat_creation_name_required" = "* Name is required";
"cat_creation_skip" = "These fields are optional. You can skip to the next step, but providing accurate information will help Caby make better judgments.";

"cat_creation_cancel_confirmation_title" = "Cancel Profile Creation?";
"cat_creation_cancel_confirmation_message" = "Are you sure you want to cancel? All information entered will be lost.";

"cat_creation_invalid_photos_title" = "Invalid Photos";
"cat_creation_invalid_photos_message" = "Some photos could not be validated. Please make sure the photos clearly show your cat.";

/* Errors */
"error_title" = "Error";
"ok" = "OK";
"cat_creation_error_invalid_name" = "Please give your cat a name";
"cat_creation_error_name_too_long" = "The name is too long (maximum 256 characters)";
"cat_creation_error_invalid_weight" = "If you don't know the cat's weight, you can select 'Unknown' to skip";
"cat_creation_error_invalid_photo" = "Photo validation failed, please try again";
"cat_creation_error_submission" = "Failed to submit the profile. Please try again."; 
"cat_creation_error_name_invalid" = "Name is invalid";
"cat_creation_error_no_photos" = "Please add at least ONE valid photo";

// Cat profile related
"cat_neutered_status" = "Neutered";
"cat_health_status_label" = "Health Status";
"cat_profile_description" = "Cat profile created via CabyCare";
"cat_avatar_label" = "Avatar";
"cat_gender_male" = "Male";
"cat_gender_female" = "Female";

// Cat status values
"cat_status_unknown" = "Unknown";
"cat_status_not_neutered" = "Not Neutered";
"cat_weight_unknown" = "Unknown";
"cat_age_unknown" = "Unknown";

// Health status
"cat_health_healthy" = "Healthy";
"cat_health_needs_attention" = "Needs Attention";
"cat_health_sick" = "Sick";

// Settings
"settings_title" = "Settings";
"settings_logout" = "Logout";
"settings_logout_message" = "Are you sure you want to logout?";
"settings_logout_confirm" = "Logout";
"settings_logout_cancel" = "Cancel";

// Notification settings
"notification_settings_title" = "Notification Settings";
"notification_daily_reminder" = "Daily Reminder";
"notification_daily_reminder_description" = "Includes reminders for cat toilet, feeding, and other daily behaviors";
"notification_health_alert" = "Health Alert";
"notification_health_description" = "Alerts when your cat's health status is abnormal";
"notification_weekly_reports" = "Including weekly activity reports and health statistics";
"notification_health_safety_notice" = "These notification types are related to your cat's health and safety and cannot be turned off";
"notification_statistics_alert" = "Statistics Alert";
"notification_abnormal_alert" = "Abnormal Alert";
"notification_abnormal_description" = "Alerts when abnormal behavior or suspicious objects are detected";
"notification_maintenance_alert" = "Maintenance Alert";
"notification_maintenance_description" = "Device maintenance and cleaning related alerts";
"notification_important_title" = "Important Notifications";
"notification_always_on" = "Always On";
"notification_types_title" = "Notification Types";
"notification_quiet_time" = "Quiet Time";
"notification_start_time" = "Start Time";
"notification_end_time" = "End Time";
"notification_current_setting" = "Current Setting:";
"notification_quiet_time_footer" = "During the set time period, only emergency notifications will be sent";
"notification_cancel" = "Cancel";
"notification_save" = "Save";
"notification_settings_footer" = "During the set time period, only emergency notifications will be sent";
"notification_enabled_count" = "Enabled";

// Home View
"home_title" = "Home";
"home_today_overview" = "Today's Overview";
"home_recent_notifications" = "Recent Notifications";
"home_interaction_time" = "Interaction Time";
"home_feeding_count" = "Feeding Count";
"home_activity_level" = "Activity Level";
"home_feeding_reminder" = "Feeding Reminder";
"home_feeding_message" = "Time to prepare dinner for kitty";
"home_interaction_reminder" = "Interaction Reminder";
"home_interaction_message" = "Haven't played with kitty today";
"home_health_reminder" = "Health Reminder";
"home_health_message" = "Time to clean the litter box";
"home_time_minutes_ago" = "minutes ago";
"home_time_hour_ago" = "hour ago";

// New Home View - Toilet Statistics
"home_alert_title" = "Attention Needed";
"home_toilet_stats_title" = "Last 24 Hours Statistics";
"home_toilet_count" = "Toilet Visits";
"home_toilet_duration" = "Total Duration";
"home_weight_change" = "Weight Change";
"home_loading_data" = "Loading data...";
"home_calculating_stats" = "Calculating statistics...";
"home_calculating_message" = "Computing cat statistics, please wait";
"home_no_data_title" = "No Data";
"home_no_data_message" = "Statistics will appear here when toilet records are available";
"home_comparison_vs" = "vs";
"home_comparison_info_title" = "Comparison Info";
"home_comparison_info_message" = "Shows data comparison between last 24 hours and previous 24 hours";
"home_weight_change_info_title" = "Weight Change Info";
"home_weight_change_info_message" = "Shows the weight change percentage compared to the previous day. Positive numbers indicate weight gain, negative numbers indicate weight loss. It's recommended to monitor your cat's weight change trends regularly.";
"home_comparison_previous_period" = "vs previous period";
"home_duration_minutes" = " min";
"home_no_toilet_records" = "No toilet records";
"home_hours_no_toilet" = "hours without toilet visit";
"home_alert_no_toilet_prefix" = "";
"home_alert_no_toilet_suffix" = "hasn't used the toilet for";
"home_alert_hours_suffix" = "hours";

// Welcome Messages for First Time Users
"home_welcome_title" = "Welcome to CabyCare";
"home_welcome_message" = "Start monitoring your cat's health";
"home_welcome_instruction" = "Please ensure your device is connected and has cat usage records";
"home_setup_step1" = "Ensure device is properly connected";
"home_setup_step2" = "Add your cat's profile";
"home_setup_step3" = "Wait for your cat to use the device";

// Cat Management
"animals_tab_normal" = "Normal";
"animals_tab_hidden" = "Hidden";
"animals_loading_hidden" = "Loading hidden cats...";
"animals_no_hidden_cats_message" = "No hidden cats";

// Cat Actions
"cat_edit_menu_title" = "Edit Cat";
"cat_action_edit" = "Edit";
"cat_action_hide" = "Hide";
"cat_action_restore" = "Restore";
"cat_action_delete" = "Delete";
"cat_delete_confirmation_title" = "Delete Cat";
"cat_delete_confirmation_message" = "Are you sure you want to delete this cat? This action cannot be undone.";
"cancel" = "Cancel";

// Cat Edit
"cat_edit_title" = "Edit Cat Profile";
"cat_edit_save" = "Save";

// Device & Group Management
"device_and_group_title" = "Devices & Family Groups";
"device_group_picker_label" = "View Selector";
"device_management_tab" = "Device Management";
"family_group_tab" = "Family Groups";
"create_new_group" = "Create New Group";
"invite_user_to_group" = "Invite User to Group";

// Device Status & Info
"current_selected_device" = "Currently Selected Device";
"firmware_version" = "Firmware Version: %@";
"device_status_online" = "Online";
"device_status_offline" = "Offline";
"view_details" = "View Details";
"auto_refresh" = "Auto Refresh";
"auto_refreshing" = "Auto Refreshing";
"select_device_prompt" = "Please select a device from the list above";
"refresh_device_status" = "Refresh Device Status";

// Group Info
"group_member_count" = "%d Members";
"group_device_count" = "%d Devices";
"select_group_prompt" = "Please select a family group from the list above";
"group_sharing_hint" = "Members in a family group can share device data";

// Group Roles
"group_role_owner" = "Owner";
"group_role_admin" = "Admin";
"group_role_member" = "Member";

// Device History & Sharing
"device_history_title" = "%@'s Historical Data";
"device_history_development" = "Feature under development...";
"device_history_nav_title" = "Historical Data";
"device_sharing_title" = "%@'s Sharing Settings";
"device_sharing_development" = "Feature under development...";
"device_sharing_nav_title" = "Sharing Settings";

// Add Device
"back" = "Back";
"add_device_method_title" = "Choose Device Addition Method";
"add_device_method_description" = "Please select the most suitable device addition method for you";

// Bluetooth Configuration
"bluetooth_config_device_title" = "Bluetooth Configure Device";
"bluetooth_config_device_subtitle" = "Automatically discover and configure AbyBox devices";
"bluetooth_config_device_description" = "Connect via Bluetooth to automatically configure WiFi and user information";
"recommend_bluetooth_config" = "Bluetooth Configuration Recommended";
"bluetooth_config_benefits" = "Bluetooth configuration is faster and more accurate";
"recommended" = "Recommended";

// Manual Add Device
"manual_add_device_title" = "Manual Add Device";
"manual_add_device_subtitle" = "Add device by entering device information";
"manual_add_device_description" = "Add device by manually entering device information";
"device_info_section" = "Device Information";
"device_name_placeholder" = "Device Name";
"device_location_placeholder" = "Device Location";
"adding_device" = "Adding Device";
"add" = "Add";
"success" = "Success";
"device_add_success" = "Device %@ added successfully";
/* Video related strings */
"video_cat_unknown" = "Unknown Cat";

// Video Duration
"video_duration_minutes_only" = "%d min";
"video_duration_minutes_seconds" = "%d min %d sec";
"video_duration_seconds_only" = "%d sec";

// Summary Duration
"summary_duration_hours_minutes" = "%d hr %d min";
"summary_duration_minutes_only" = "%d min";

/* Chart Data Types */
"chart_data_toilet" = "Toilet Usage";
"chart_data_weight" = "Weight";

/* Chart Units */
"chart_unit_times" = "times";
"chart_unit_kg" = "kg";
