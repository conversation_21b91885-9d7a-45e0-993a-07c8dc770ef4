import Foundation

struct Configuration {
    struct API {
        static let cabyBaseUrl = "https://api.caby.care"
        // static let cabyBaseUrl = "http://***************:5678"
        // static let cabyBaseUrl = "http://localhost:5678"

        struct Logto {
            // Logto SDK配置 - 仅保留SDK需要的基本配置
            static let logtoBase = "https://login.caby.care"
            static let clientId = "d2oz2qj0ppjdpf6de14ha"
            static let appCallback = "api.cabycare://callback"
        }

        struct Client {
            static let registerPath = "\(cabyBaseUrl)/api/clients/register"
            static func getClientPath(clientId: String) -> String {
                "\(cabyBaseUrl)/api/clients/\(clientId)"
            }
            static func clientTokenPath(clientId: String) -> String {
                "\(cabyBaseUrl)/api/clients/\(clientId)/token"
            }
        }

        struct Notification {
            static let settingsPath = "\(cabyBaseUrl)/api/notifications/settings"
            static func settingsPath(userId: String) -> String {
                "\(cabyBaseUrl)/api/notifications/settings?user_id=\(userId)"
            }
        }

        struct Cat {
            static let basePath = "\(cabyBaseUrl)/api/cats"
            static let createPath = "\(cabyBaseUrl)/api/cats"
            static let listPath = "\(cabyBaseUrl)/api/cats"
        }

        struct Video {
            static func listPath(deviceId: String, start: String? = nil, end: String? = nil) -> String {
                var path = "\(cabyBaseUrl)/api/records/videos/list?path=device\(deviceId)"
                if let start = start {
                    path += "&start=\(start)"
                }
                if let end = end {
                    path += "&end=\(end)"
                }
                return path
            }
            static func segmentPath(segmentUrl: String) -> String {
                return cabyBaseUrl + segmentUrl
            }
        }

        struct User {
            // 唯一存在的用户相关端点
            static func getUserInfoPath() -> String {
                "\(cabyBaseUrl)/api/user/info"
            }
            
            // 注意：用户创建只能通过 /api/callback 完成
            // 以下端点不存在：
            // - /api/user/create
            // - /api/user/register  
            // - /api/users
        }

        struct Device {
            static func getUserDevicesPath(userId: String) -> String {
                "\(cabyBaseUrl)/api/users/\(userId)/devices"
            }
            static func getDeviceInfoPath(deviceId: String) -> String {
                "\(cabyBaseUrl)/api/devices/\(deviceId)"
            }
            
            // 获取用户可访问的所有设备
            static func getAccessibleDevicesPath(userId: String) -> String {
                "\(cabyBaseUrl)/api/devices/accessible?user_id=\(userId)"
            }
            
            // 获取设备状态 - 基于test_device_status.sh中的API
            static func getDeviceStatusPath(deviceId: String) -> String {
                "\(cabyBaseUrl)/api/devices/\(deviceId)/status"
            }
            
            // 注册新设备
            static func registerDevicePath() -> String {
                "\(cabyBaseUrl)/api/devices/register"
            }
            
            // 更新设备信息
            static func updateDevicePath(deviceId: String) -> String {
                "\(cabyBaseUrl)/api/devices/\(deviceId)"
            }
            
            // 删除设备
            static func deleteDevicePath(deviceId: String) -> String {
                "\(cabyBaseUrl)/api/devices/\(deviceId)"
            }
        }
        
        // 家庭组相关API
        struct FamilyGroup {
            // 获取用户的所有家庭组
            static func getFamilyGroupsPath(userId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups?user_id=\(userId)"
            }
            
            // 创建家庭组
            static func createFamilyGroupPath() -> String {
                "\(cabyBaseUrl)/api/family-groups"
            }
            
            // 获取家庭组详情
            static func getFamilyGroupDetailPath(groupId: String, userId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)?user_id=\(userId)"
            }
            
            // 更新家庭组
            static func updateFamilyGroupPath(groupId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)"
            }
            
            // 删除家庭组
            static func deleteFamilyGroupPath(groupId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)"
            }
            
            // 添加成员
            static func addMemberPath(groupId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)/members"
            }
            
            // 更新成员
            static func updateMemberPath(groupId: String, memberId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)/members/\(memberId)"
            }
            
            // 移除成员
            static func removeMemberPath(groupId: String, memberId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)/members/\(memberId)"
            }
            
            // 添加设备
            static func addDevicePath(groupId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)/devices"
            }
            
            // 移除设备
            static func removeDevicePath(groupId: String, deviceId: String) -> String {
                "\(cabyBaseUrl)/api/family-groups/\(groupId)/devices/\(deviceId)"
            }
            
            // 邀请相关
            struct Invitation {
                // 获取收到的邀请
                static func getReceivedInvitationsPath(userId: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/invitations/received?user_id=\(userId)"
                }
                
                // 获取发送的邀请
                static func getSentInvitationsPath(userId: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/invitations/sent?user_id=\(userId)"
                }
                
                // 创建邀请 (通过用户ID)
                static func createInvitationPath(groupId: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/\(groupId)/invitations"
                }
                
                // 创建邀请 (通过邮箱)
                static func createEmailInvitationPath(groupId: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/\(groupId)/invitations/email"
                }
                
                // 处理邀请
                static func processInvitationPath(invitationId: String, action: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/invitations/\(invitationId)/process?action=\(action)"
                }
                
                // 取消邀请
                static func cancelInvitationPath(invitationId: String) -> String {
                    "\(cabyBaseUrl)/api/family-groups/invitations/\(invitationId)/cancel"
                }
            }
        }
    }

    struct User {
        static var defaultUserId: String {
            // 直接使用 UserDefaultsManager
            guard let userId = UserDefaultsManager.shared.userId else {
                Log.warning("⚠️ 未找到用户ID")
                return ""
            }
            return userId
        }
    }

    struct DateFormat {
        static let apiDateFormat = "yyyy-MM-dd"

        static let shared: Foundation.DateFormatter = {
            let formatter = Foundation.DateFormatter()
            return formatter
        }()

        // 使用 nonisolated(unsafe) 来标记为并发安全的静态属性
        // ISO8601DateFormatter 虽然不是 Sendable，但在实际使用中是线程安全的
        nonisolated(unsafe) static let iso8601Formatter: ISO8601DateFormatter = {
            let formatter = ISO8601DateFormatter()
            return formatter
        }()

        static func formatAPIDate(_ date: Date) -> String {
            DateFormatterUtil.format(date, as: .api)
        }

        static func formatISO8601(_ date: Date) -> String {
            DateFormatterUtil.format(date, as: .iso8601)
        }

        static func format(_ date: Date, format: String) -> String {
            shared.dateFormat = format
            return shared.string(from: date)
        }

        static func formatTime(_ date: Date) -> String {
            DateFormatterUtil.format(date, as: .time)
        }

        static func formatDisplayDate(_ date: Date) -> String {
            DateFormatterUtil.format(date, as: .display)
        }
    }

    struct CatProfileCreation {
        static let totalSteps = 6
        static let minCatConfidence: Float = 0.6
        static let maxPhotos = 9
        static let photoCompressionQuality: CGFloat = 0.7
    }
}
